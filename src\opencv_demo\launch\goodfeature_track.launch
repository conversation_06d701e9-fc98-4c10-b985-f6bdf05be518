<launch>
  <arg name="node_name" default="goodfeature_track" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="max_corners" default="23" doc="Maximum number of corners to return. If there are more corners than are found, the strongest  of them is returned." />

  <!-- goodfeature_track.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="goodfeature_track" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="max_corners" value="$(arg max_corners)" />
  </node>
</launch>
