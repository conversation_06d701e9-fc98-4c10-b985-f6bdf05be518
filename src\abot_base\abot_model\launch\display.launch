<launch>
  <arg
    name="model" />
  <arg
    name="gui"
    default="False" />
  <param
    name="robot_description"
    textfile="$(find abot_model)/urdf/abot_model.urdf" />
  <param
    name="use_gui"
    value="$(arg gui)" />
  <node
    name="joint_state_publisher"
    pkg="joint_state_publisher"
    type="joint_state_publisher" />
  <node
    name="robot_state_publisher"
    pkg="robot_state_publisher"
    type="state_publisher" />
  <!--node
    name="rviz"
    pkg="rviz"
    type="rviz"
    args="-d $(find abot_model)/urdf.rviz" /-->
</launch>
