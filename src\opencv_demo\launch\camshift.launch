<launch>
  <arg name="node_name" default="camshift" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="histogram" default="[0.0, 255.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]" doc="Histogram of tracked color object" />
  <arg name="vmin" default="10" doc="Min threshould of lightness."/>
  <arg name="vmax" default="230" doc="Max threshould of lightness." />
  <arg name="smin" default="60" doc="Min value of saturation." />

  <!-- camshift.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="camshift" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <rosparam param="histogram" subst_value="True">
      $(arg histogram)
    </rosparam>
    <param name="vmin" value="$(arg vmin)" />
    <param name="vmax" value="$(arg vmax)" />
    <param name="smin" value="$(arg smin)" />
  </node>
</launch>
