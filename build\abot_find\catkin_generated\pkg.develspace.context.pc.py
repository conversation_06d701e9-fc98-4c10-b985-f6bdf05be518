# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/310319/devel/include".split(';') if "/home/<USER>/310319/devel/include" != "" else []
PROJECT_CATKIN_DEPENDS = "cv_bridge;roscpp;rospy;sensor_msgs;std_msgs;image_transport;message_filters;tf;message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "find_object_2d"
PROJECT_SPACE_DIR = "/home/<USER>/310319/devel"
PROJECT_VERSION = "0.6.4"
