abot_find/src/CMakeFiles/find_object.dir/JsonWriter.cpp.o: \
 /home/<USER>/310319/src/abot_find/src/JsonWriter.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/JsonWriter.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/FindObjectExp.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/DetectionInfo.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMultiMap \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h \
 /usr/include/c++/7/type_traits \
 /usr/include/x86_64-linux-gnu/c++/7/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/os_defines.h \
 /usr/include/features.h /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/cpu_defines.h \
 /usr/include/c++/7/cstddef \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/stddef.h \
 /usr/include/c++/7/utility /usr/include/c++/7/bits/stl_relops.h \
 /usr/include/c++/7/bits/stl_pair.h /usr/include/c++/7/bits/move.h \
 /usr/include/c++/7/bits/concept_check.h \
 /usr/include/c++/7/initializer_list \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h \
 /usr/include/c++/7/algorithm /usr/include/c++/7/bits/stl_algobase.h \
 /usr/include/c++/7/bits/functexcept.h \
 /usr/include/c++/7/bits/exception_defines.h \
 /usr/include/c++/7/bits/cpp_type_traits.h \
 /usr/include/c++/7/ext/type_traits.h \
 /usr/include/c++/7/ext/numeric_traits.h \
 /usr/include/c++/7/bits/stl_iterator_base_types.h \
 /usr/include/c++/7/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/7/debug/assertions.h \
 /usr/include/c++/7/bits/stl_iterator.h \
 /usr/include/c++/7/bits/ptr_traits.h /usr/include/c++/7/debug/debug.h \
 /usr/include/c++/7/bits/predefined_ops.h \
 /usr/include/c++/7/bits/stl_algo.h /usr/include/c++/7/cstdlib \
 /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap-16.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/sys/sysmacros.h \
 /usr/include/x86_64-linux-gnu/bits/sysmacros.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/alloca.h /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/7/bits/std_abs.h /usr/include/c++/7/bits/algorithmfwd.h \
 /usr/include/c++/7/bits/stl_heap.h /usr/include/c++/7/bits/stl_tempbuf.h \
 /usr/include/c++/7/bits/stl_construct.h /usr/include/c++/7/new \
 /usr/include/c++/7/exception /usr/include/c++/7/bits/exception.h \
 /usr/include/c++/7/bits/exception_ptr.h \
 /usr/include/c++/7/bits/cxxabi_init_exception.h \
 /usr/include/c++/7/typeinfo /usr/include/c++/7/bits/hash_bytes.h \
 /usr/include/c++/7/bits/nested_exception.h \
 /usr/include/c++/7/ext/alloc_traits.h \
 /usr/include/c++/7/bits/alloc_traits.h \
 /usr/include/c++/7/bits/memoryfwd.h \
 /usr/include/c++/7/bits/uniform_int_dist.h /usr/include/c++/7/limits \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h \
 /usr/include/c++/7/atomic /usr/include/c++/7/bits/atomic_base.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/include/c++/7/bits/atomic_lockfree_defines.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h \
 /usr/include/string.h /usr/include/strings.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h \
 /usr/include/c++/7/numeric /usr/include/c++/7/bits/stl_numeric.h \
 /usr/include/c++/7/iterator /usr/include/c++/7/ostream \
 /usr/include/c++/7/ios /usr/include/c++/7/iosfwd \
 /usr/include/c++/7/bits/stringfwd.h /usr/include/c++/7/bits/postypes.h \
 /usr/include/c++/7/cwchar /usr/include/wchar.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/c++/7/bits/char_traits.h /usr/include/c++/7/cstdint \
 /usr/include/c++/7/bits/localefwd.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/c++locale.h \
 /usr/include/c++/7/clocale /usr/include/locale.h \
 /usr/include/x86_64-linux-gnu/bits/locale.h /usr/include/c++/7/cctype \
 /usr/include/ctype.h /usr/include/c++/7/bits/ios_base.h \
 /usr/include/c++/7/ext/atomicity.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/gthr.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/x86_64-linux-gnu/bits/sched.h \
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/timex.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/x86_64-linux-gnu/bits/setjmp.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/atomic_word.h \
 /usr/include/c++/7/bits/locale_classes.h /usr/include/c++/7/string \
 /usr/include/c++/7/bits/allocator.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/c++allocator.h \
 /usr/include/c++/7/ext/new_allocator.h \
 /usr/include/c++/7/bits/ostream_insert.h \
 /usr/include/c++/7/bits/cxxabi_forced.h \
 /usr/include/c++/7/bits/stl_function.h \
 /usr/include/c++/7/backward/binders.h \
 /usr/include/c++/7/bits/range_access.h \
 /usr/include/c++/7/bits/basic_string.h \
 /usr/include/c++/7/ext/string_conversions.h /usr/include/c++/7/cstdio \
 /usr/include/stdio.h /usr/include/x86_64-linux-gnu/bits/libio.h \
 /usr/include/x86_64-linux-gnu/bits/_G_config.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/sys_errlist.h \
 /usr/include/c++/7/cerrno /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/c++/7/bits/functional_hash.h \
 /usr/include/c++/7/bits/basic_string.tcc \
 /usr/include/c++/7/bits/locale_classes.tcc \
 /usr/include/c++/7/system_error \
 /usr/include/x86_64-linux-gnu/c++/7/bits/error_constants.h \
 /usr/include/c++/7/stdexcept /usr/include/c++/7/streambuf \
 /usr/include/c++/7/bits/streambuf.tcc \
 /usr/include/c++/7/bits/basic_ios.h \
 /usr/include/c++/7/bits/locale_facets.h /usr/include/c++/7/cwctype \
 /usr/include/wctype.h /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/ctype_base.h \
 /usr/include/c++/7/bits/streambuf_iterator.h \
 /usr/include/x86_64-linux-gnu/c++/7/bits/ctype_inline.h \
 /usr/include/c++/7/bits/locale_facets.tcc \
 /usr/include/c++/7/bits/basic_ios.tcc \
 /usr/include/c++/7/bits/ostream.tcc /usr/include/c++/7/istream \
 /usr/include/c++/7/bits/istream.tcc \
 /usr/include/c++/7/bits/stream_iterator.h /usr/include/c++/7/list \
 /usr/include/c++/7/bits/stl_list.h \
 /usr/include/c++/7/bits/allocated_ptr.h \
 /usr/include/c++/7/ext/aligned_buffer.h /usr/include/c++/7/bits/list.tcc \
 /usr/include/c++/7/stdlib.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include-fixed/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include-fixed/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h \
 /usr/include/c++/7/map /usr/include/c++/7/bits/stl_tree.h \
 /usr/include/c++/7/bits/stl_map.h /usr/include/c++/7/tuple \
 /usr/include/c++/7/array /usr/include/c++/7/bits/uses_allocator.h \
 /usr/include/c++/7/bits/invoke.h /usr/include/c++/7/bits/stl_multimap.h \
 /usr/include/c++/7/functional /usr/include/c++/7/bits/std_function.h \
 /usr/include/c++/7/bits/refwrap.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QTransform \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h \
 /usr/include/c++/7/vector /usr/include/c++/7/bits/stl_uninitialized.h \
 /usr/include/c++/7/bits/stl_vector.h \
 /usr/include/c++/7/bits/stl_bvector.h /usr/include/c++/7/bits/vector.tcc \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h \
 /usr/include/c++/7/chrono /usr/include/c++/7/ratio \
 /usr/include/c++/7/ctime /usr/include/c++/7/bits/parse_numbers.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h \
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QString \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h \
 /usr/include/opencv2/features2d/features2d.hpp \
 /usr/include/opencv2/features2d.hpp /usr/include/opencv2/core.hpp \
 /usr/include/opencv2/core/cvdef.h \
 /usr/include/opencv2/core/hal/interface.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/emmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/xmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/mmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/mm_malloc.h \
 /usr/include/opencv2/core/version.hpp /usr/include/opencv2/core/base.hpp \
 /usr/include/opencv2/opencv_modules.hpp /usr/include/c++/7/climits \
 /usr/include/opencv2/core/cvstd.hpp /usr/include/c++/7/cstring \
 /usr/include/c++/7/cmath /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/opencv2/core/ptr.inl.hpp \
 /usr/include/opencv2/core/neon_utils.hpp \
 /usr/include/opencv2/core/traits.hpp /usr/include/opencv2/core/matx.hpp \
 /usr/include/opencv2/core/saturate.hpp \
 /usr/include/opencv2/core/fast_math.hpp \
 /usr/include/opencv2/core/types.hpp /usr/include/c++/7/cfloat \
 /usr/lib/gcc/x86_64-linux-gnu/7/include/float.h \
 /usr/include/opencv2/core/mat.hpp \
 /usr/include/opencv2/core/bufferpool.hpp \
 /usr/include/opencv2/core/mat.inl.hpp \
 /usr/include/opencv2/core/persistence.hpp \
 /usr/include/opencv2/core/operations.hpp \
 /usr/include/opencv2/core/cvstd.inl.hpp /usr/include/c++/7/complex \
 /usr/include/c++/7/sstream /usr/include/c++/7/bits/sstream.tcc \
 /usr/include/opencv2/core/utility.hpp /usr/include/opencv2/core/core_c.h \
 /usr/include/opencv2/core/types_c.h /usr/include/assert.h \
 /usr/include/opencv2/core/optim.hpp /usr/include/opencv2/core/ovx.hpp \
 /usr/include/opencv2/core/cvdef.h \
 /usr/include/opencv2/flann/miniflann.hpp \
 /usr/include/opencv2/flann/defines.h /usr/include/opencv2/flann/config.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/utilite/ULogger.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/utilite/UMutex.h \
 /home/<USER>/310319/src/abot_find/src/../include/find_object/utilite/UDestroyer.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFile \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QTextStream \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h \
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h \
 /home/<USER>/310319/src/abot_find/src/json/json.h /usr/include/c++/7/deque \
 /usr/include/c++/7/bits/stl_deque.h /usr/include/c++/7/bits/deque.tcc \
 /usr/include/c++/7/stack /usr/include/c++/7/bits/stl_stack.h \
 /usr/include/c++/7/iostream
