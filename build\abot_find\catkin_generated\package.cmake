set(_CATKIN_CURRENT_PACKAGE "find_object_2d")
set(find_object_2d_VERSION "0.6.4")
set(find_object_2d_MAINTAINER "Mathieu Labbe <<EMAIL>>")
set(find_object_2d_PACKAGE_FORMAT "1")
set(find_object_2d_BUILD_DEPENDS "message_generation" "qtbase5-dev" "cv_bridge" "roscpp" "rospy" "sensor_msgs" "std_msgs" "std_srvs" "image_transport" "message_filters" "tf")
set(find_object_2d_BUILD_EXPORT_DEPENDS "message_runtime" "qtbase5-dev" "cv_bridge" "roscpp" "rospy" "sensor_msgs" "std_msgs" "std_srvs" "image_transport" "message_filters" "tf")
set(find_object_2d_BUILDTOOL_DEPENDS "catkin")
set(find_object_2d_BUILDTOOL_EXPORT_DEPENDS )
set(find_object_2d_EXEC_DEPENDS "message_runtime" "qtbase5-dev" "cv_bridge" "roscpp" "rospy" "sensor_msgs" "std_msgs" "std_srvs" "image_transport" "message_filters" "tf")
set(find_object_2d_RUN_DEPENDS "message_runtime" "qtbase5-dev" "cv_bridge" "roscpp" "rospy" "sensor_msgs" "std_msgs" "std_srvs" "image_transport" "message_filters" "tf")
set(find_object_2d_TEST_DEPENDS )
set(find_object_2d_DOC_DEPENDS )
set(find_object_2d_URL_WEBSITE "http://find-object.googlecode.com")
set(find_object_2d_URL_BUGTRACKER "")
set(find_object_2d_URL_REPOSITORY "")
set(find_object_2d_DEPRECATED "")