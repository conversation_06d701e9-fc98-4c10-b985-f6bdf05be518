#!/bin/bash

# Gmapping完整系统分析脚本
# 检查所有必需的组件和文件

WORKSPACE_PATH="$HOME/310319"

echo "=== Gmapping完整系统分析 ==="
echo "分析工作空间: $WORKSPACE_PATH"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 1. 系统架构分析
print_section "1. Gmapping系统架构分析"
echo "Gmapping系统包含以下层次:"
echo "┌─────────────────────────────────────┐"
echo "│           应用层                    │"
echo "│  - 键盘遥控 (teleop_twist_keyboard) │"
echo "│  - RViz可视化                       │"
echo "├─────────────────────────────────────┤"
echo "│           算法层                    │"
echo "│  - Gmapping SLAM算法                │"
echo "│  - 地图构建和定位                   │"
echo "├─────────────────────────────────────┤"
echo "│           数据处理层                │"
echo "│  - 激光雷达数据处理                 │"
echo "│  - IMU数据融合                      │"
echo "│  - 里程计数据处理                   │"
echo "├─────────────────────────────────────┤"
echo "│           硬件驱动层                │"
echo "│  - 激光雷达驱动                     │"
echo "│  - IMU驱动                          │"
echo "│  - 底盘驱动                         │"
echo "└─────────────────────────────────────┘"
echo ""

# 2. 核心文件检查
print_section "2. 核心启动文件检查"

core_files=(
    "1-gmapping.sh:主启动脚本"
    "src/robot_slam/launch/gmapping.launch:Gmapping主配置"
    "src/robot_slam/launch/include/gmapping.launch.xml:Gmapping参数配置"
    "src/robot_slam/launch/view_mapping.launch:RViz启动配置"
    "src/abot_base/abot_bringup/launch/robot_with_imu.launch:硬件主启动"
)

for file_info in "${core_files[@]}"; do
    file_path=$(echo $file_info | cut -d':' -f1)
    description=$(echo $file_info | cut -d':' -f2)
    
    if [ -f "$WORKSPACE_PATH/$file_path" ]; then
        print_success "$description - $file_path"
    else
        print_error "$description 缺失 - $file_path"
    fi
done
echo ""

# 3. 硬件驱动文件检查
print_section "3. 硬件驱动文件检查"

hardware_files=(
    "src/abot_base/abot_bringup/launch/bringup_with_imu.launch:底盘IMU驱动"
    "src/abot_base/abot_bringup/launch/rplidar.launch:激光雷达驱动"
    "src/abot_base/abot_bringup/launch/model.launch:机器人模型"
    "src/abot_base/abot_bringup/params/base_params_with_imu.yaml:底盘参数"
)

for file_info in "${hardware_files[@]}"; do
    file_path=$(echo $file_info | cut -d':' -f1)
    description=$(echo $file_info | cut -d':' -f2)
    
    if [ -f "$WORKSPACE_PATH/$file_path" ]; then
        print_success "$description - $file_path"
    else
        print_error "$description 缺失 - $file_path"
    fi
done
echo ""

# 4. 可视化配置文件检查
print_section "4. 可视化配置文件检查"

viz_files=(
    "src/robot_slam/rviz/view_gmapping.rviz:Gmapping RViz配置"
    "src/robot_slam/rviz/view_navigation.rviz:导航RViz配置"
)

for file_info in "${viz_files[@]}"; do
    file_path=$(echo $file_info | cut -d':' -f1)
    description=$(echo $file_info | cut -d':' -f2)
    
    if [ -f "$WORKSPACE_PATH/$file_path" ]; then
        print_success "$description - $file_path"
    else
        print_error "$description 缺失 - $file_path"
    fi
done
echo ""

# 5. 源代码文件检查
print_section "5. 源代码文件检查"

source_files=(
    "src/abot_base/abot_bringup/src/main.cpp:底盘驱动主程序"
    "src/abot_base/abot_bringup/src/base_driver.cpp:底盘驱动核心"
    "src/abot_base/abot_bringup/scripts/odom_ekf.py:里程计融合脚本"
    "src/robot_slam/src/navigate.cpp:导航相关源码"
)

for file_info in "${source_files[@]}"; do
    file_path=$(echo $file_info | cut -d':' -f1)
    description=$(echo $file_info | cut -d':' -f2)
    
    if [ -f "$WORKSPACE_PATH/$file_path" ]; then
        print_success "$description - $file_path"
    else
        print_warning "$description 缺失 - $file_path"
    fi
done
echo ""

# 6. 依赖包检查
print_section "6. ROS依赖包检查"

if [ -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    source "$WORKSPACE_PATH/devel/setup.bash"
    
    required_packages=(
        "gmapping:SLAM算法包"
        "teleop_twist_keyboard:键盘遥控包"
        "rviz:可视化工具"
        "tf:坐标变换包"
        "sensor_msgs:传感器消息包"
        "nav_msgs:导航消息包"
        "geometry_msgs:几何消息包"
        "rplidar_ros:激光雷达ROS驱动"
        "imu_filter_madgwick:IMU滤波器"
        "robot_pose_ekf:位姿融合包"
    )
    
    for pkg_info in "${required_packages[@]}"; do
        pkg_name=$(echo $pkg_info | cut -d':' -f1)
        description=$(echo $pkg_info | cut -d':' -f2)
        
        if rospack find $pkg_name >/dev/null 2>&1; then
            print_success "$description - $pkg_name"
        else
            print_error "$description 缺失 - $pkg_name"
            echo "   安装命令: sudo apt-get install ros-\$ROS_DISTRO-$pkg_name"
        fi
    done
else
    print_error "工作空间未编译，无法检查ROS包依赖"
fi
echo ""

# 7. 数据流分析
print_section "7. 数据流分析"
echo "Gmapping系统的数据流:"
echo ""
echo "硬件传感器 → ROS话题 → Gmapping算法 → 地图输出"
echo ""
echo "详细数据流:"
echo "1. 激光雷达 → /scan → slam_gmapping"
echo "2. 底盘编码器 → /wheel_odom → robot_pose_ekf → /odom → slam_gmapping"
echo "3. IMU → /imu/data → robot_pose_ekf → /odom → slam_gmapping"
echo "4. slam_gmapping → /map → RViz显示"
echo "5. 键盘输入 → /cmd_vel → 底盘驱动 → 机器人移动"
echo ""

# 8. 关键话题检查
print_section "8. 关键话题检查"
echo "Gmapping系统需要的关键话题:"

required_topics=(
    "/scan:激光雷达扫描数据"
    "/odom:里程计数据"
    "/imu/data:IMU数据"
    "/cmd_vel:速度控制命令"
    "/map:地图数据"
    "/map_metadata:地图元数据"
    "/tf:坐标变换"
)

for topic_info in "${required_topics[@]}"; do
    topic_name=$(echo $topic_info | cut -d':' -f1)
    description=$(echo $topic_info | cut -d':' -f2)
    echo "  $topic_name - $description"
done
echo ""

# 9. 系统完整性评估
print_section "9. 系统完整性评估"

echo "基于文件检查结果，评估系统完整性:"
echo ""

if [ -f "$WORKSPACE_PATH/1-gmapping.sh" ] && \
   [ -f "$WORKSPACE_PATH/src/robot_slam/launch/gmapping.launch" ] && \
   [ -f "$WORKSPACE_PATH/src/abot_base/abot_bringup/launch/robot_with_imu.launch" ]; then
    print_success "核心文件完整 - 系统可以启动"
else
    print_error "核心文件缺失 - 系统无法正常启动"
fi

if [ -f "$WORKSPACE_PATH/src/robot_slam/rviz/view_gmapping.rviz" ]; then
    print_success "可视化配置完整 - RViz可以正常显示"
else
    print_warning "RViz配置缺失 - 可能影响可视化效果"
fi

echo ""
print_section "10. 建议和总结"
echo "要实现完整的Gmapping功能，您需要:"
echo ""
echo "必需组件 (缺一不可):"
echo "1. ✅ 硬件驱动层 - 激光雷达、IMU、底盘驱动"
echo "2. ✅ 数据处理层 - 传感器数据融合和预处理"
echo "3. ✅ SLAM算法层 - Gmapping核心算法"
echo "4. ✅ 可视化层 - RViz配置和显示"
echo "5. ✅ 控制层 - 键盘遥控或路径规划"
echo ""
echo "可选组件 (增强功能):"
echo "1. 🔧 地图保存功能 - map_server"
echo "2. 🔧 导航功能 - move_base, amcl"
echo "3. 🔧 路径规划 - global_planner, local_planner"
echo ""
echo "结论: 您当前的文件集合包含了Gmapping的核心组件，"
echo "但要实现完整功能还需要确保所有依赖包正确安装和配置。"
echo ""
echo "=== 分析完成 ==="
