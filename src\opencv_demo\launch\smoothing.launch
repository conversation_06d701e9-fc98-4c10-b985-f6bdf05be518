<launch>
  <arg name="node_name" default="smoothing" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="filter_type" default="0" doc="Smoothing Filter Methods. 0:Homogeneous blur, 1:Gaussian blur, 2:Median blur, 3:Bilateral Filter." />
  <arg name="kernel_size" default="7" doc="Size of the kernel (only one because we use a square window). Must be odd." />

  <!-- smoothing.cpp  -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="smoothing" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="filter_type" value="$(arg filter_type)" />
    <param name="kernel_size" value="$(arg kernel_size)" />
  </node>
</launch>
