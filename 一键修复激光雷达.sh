#!/bin/bash

echo "=== 一键修复激光雷达问题 ==="
echo ""

# 检查激光雷达是否连接
echo "正在检查激光雷达连接..."
if ls /dev/ttyUSB* >/dev/null 2>&1; then
    echo "✅ 找到激光雷达设备"
    ls /dev/ttyUSB*
else
    echo "❌ 没有找到激光雷达设备"
    echo "请检查："
    echo "1. USB线是否插好"
    echo "2. 激光雷达是否通电"
    echo "3. 尝试换个USB口"
    exit 1
fi

echo ""
echo "正在修复权限问题..."

# 修复权限
sudo chmod 666 /dev/ttyUSB* 2>/dev/null
echo "✅ 权限修复完成"

# 创建链接
echo "正在创建设备链接..."
sudo rm -f /dev/rplidar 2>/dev/null
sudo ln -s /dev/ttyUSB0 /dev/rplidar 2>/dev/null
echo "✅ 设备链接创建完成"

# 检查结果
echo ""
echo "修复结果："
if [ -e "/dev/rplidar" ]; then
    echo "✅ /dev/rplidar 已创建成功"
    ls -l /dev/rplidar
else
    echo "❌ 创建失败，请手动检查"
fi

echo ""
echo "=== 修复完成 ==="
echo "现在可以重新运行gmapping了！"
echo ""
echo "如果还有问题，请："
echo "1. 重新插拔USB线"
echo "2. 重启电脑"
echo "3. 检查激光雷达是否损坏"
