<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>addObjectDialog</class>
 <widget class="QDialog" name="addObjectDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>527</width>
    <height>420</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Add object</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,0">
     <item>
      <widget class="QLabel" name="label_instruction">
       <property name="text">
        <string>(Instructions)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBox_selection">
       <item>
        <property name="text">
         <string>Select region</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Select keypoints</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="find_object::ObjWidget" name="cameraView" native="true"/>
     </item>
     <item>
      <widget class="find_object::ObjWidget" name="objectView" native="true"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="pushButton_cancel">
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_back">
       <property name="text">
        <string>Back</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_takePicture">
       <property name="text">
        <string>Take picture</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_next">
       <property name="text">
        <string>End</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>find_object::ObjWidget</class>
   <extends>QWidget</extends>
   <header>find_object/ObjWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
