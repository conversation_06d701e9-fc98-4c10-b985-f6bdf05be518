# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DPROJECT_NAME=\"find_object_2d\" -DPROJECT_PREFIX=\"find_object\" -DPROJECT_VERSION=\"0.6.4\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_NO_KEYWORDS -DQT_PRINTSUPPORT_LIB -DQT_WIDGETS_LIB -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"find_object_2d\" -Dfind_object_EXPORTS

CXX_INCLUDES = -I/home/<USER>/310319/devel/include -I/home/<USER>/310319/src/abot_find/src/../include -I/home/<USER>/310319/src/abot_find/src -I/home/<USER>/310319/build/abot_find/src -I/opt/ros/melodic/include -I/opt/ros/melodic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/opencv -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt5/QtNetwork -isystem /usr/include/x86_64-linux-gnu/qt5/QtPrintSupport

CXX_FLAGS = -fPIC -fPIC

