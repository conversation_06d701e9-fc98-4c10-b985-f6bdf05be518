<launch>
  <!-- 机器人启动文件 - 不包含IMU -->
  
  <!-- 底盘驱动 -->
  <node name="abot_driver" pkg="abot_bringup" type="abot_driver" output="screen">
    <rosparam file="$(find abot_bringup)/params/base_params.yaml" command="load"/>
  </node>

  <!-- 机器人模型发布 -->
  <include file="$(find abot_bringup)/launch/model.launch"/>
  
  <!-- 激光雷达 -->
  <include file="$(find abot_bringup)/launch/rplidar.launch"/>
  
  <!-- 基础tf变换 -->
  <node pkg="tf" type="static_transform_publisher" name="base_laser_to_base_link"
        args="0.06 0 0.19 0 0 0 /base_link /laser_link 40" />
        
</launch>
