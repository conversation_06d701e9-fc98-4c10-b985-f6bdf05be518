<launch>
  <node pkg="tf" type="static_transform_publisher" name="world_to_map" args="0 0 0.0 0 0 0 world map 10" />
  <node pkg="tf" type="static_transform_publisher" name="base_link_to_cam" args="0 0 0.3 0 1.57 -1.57 base_link usb_cam 10" />
  <arg name="marker_size" default="9" />
  <arg name="max_new_marker_error" default="0.08" />
  <arg name="max_track_error" default="0.2" />
  <arg name="cam_image_topic" default="/usb_cam/image_raw" />
  <arg name="cam_info_topic" default="/usb_cam/camera_info" />
  <arg name="output_frame" default="/usb_cam" />

  <node name="ar_track_alvar" pkg="ar_track_alvar" type="individualMarkersNoKinect" respawn="false" output="screen">
    <param name="marker_size" type="double" value="$(arg marker_size)" />
    <param name="max_new_marker_error" type="double" value="$(arg max_new_marker_error)" />
    <param name="max_track_error" type="double" value="$(arg max_track_error)" />
    <param name="output_frame" type="string" value="$(arg output_frame)" />
    <remap from="camera_image" to="$(arg cam_image_topic)" />
    <remap from="camera_info" to="$(arg cam_info_topic)" />
  </node>

  <node pkg="rviz" type="rviz" name="rviz" args="-d $(find track_tag)/rviz/ar_track_camera.rviz" />
</launch>
