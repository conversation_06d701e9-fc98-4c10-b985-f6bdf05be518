<launch>
  <arg name="node_name" default="hsv_color_filter" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="h_limit_max" default="113" doc="The maximum allowed field value Hue" />
  <arg name="h_limit_min" default="44" doc="The minimum allowed field value Hue" />
  <arg name="s_limit_max" default="176" doc="The maximum allowed field value Saturation" />
  <arg name="s_limit_min" default="75" doc="The minimum allowed field value Saturation" />
  <arg name="v_limit_max" default="153" doc="The maximum allowed field value Value" />
  <arg name="v_limit_min" default="113" doc="The minimum allowed field value Value" />

  <!-- color_filter.cpp  -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="hsv_color_filter" output="screen">
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="h_limit_max" value="$(arg h_limit_max)" />
    <param name="h_limit_min" value="$(arg h_limit_min)" />
    <param name="s_limit_max" value="$(arg s_limit_max)" />
    <param name="s_limit_min" value="$(arg s_limit_min)" />
    <param name="v_limit_max" value="$(arg v_limit_max)" />
    <param name="v_limit_min" value="$(arg v_limit_min)" />
  </node>
</launch>
