#!/bin/bash

###gmapping with abot - 修复版本###

# 设置工作空间路径(可根据实际情况修改)
WORKSPACE_PATH="$HOME/310319"

# 检查工作空间是否存在
if [ ! -d "$WORKSPACE_PATH" ]; then
    echo "错误: 工作空间不存在: $WORKSPACE_PATH"
    echo "请修改脚本中的WORKSPACE_PATH变量"
    exit 1
fi

# 检查setup.bash是否存在
if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    echo "错误: setup.bash不存在，请先编译工作空间:"
    echo "cd $WORKSPACE_PATH && catkin_make"
    exit 1
fi

echo "启动Gmapping系统..."
echo "工作空间: $WORKSPACE_PATH"

# 使用gnome-terminal启动多个标签页
gnome-terminal --window -e 'bash -c "echo \"启动roscore...\"; roscore; exec bash"' \
--tab -e "bash -c \"echo '等待roscore启动...'; sleep 5; echo '启动机器人硬件...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup robot_with_imu.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待硬件启动...'; sleep 8; echo '启动gmapping...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待gmapping启动...'; sleep 10; echo '启动RViz...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待系统就绪...'; sleep 12; echo '启动键盘控制...'; echo '使用 i,j,k,l,, 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py; exec bash\""

echo "所有组件已启动，请检查各个终端窗口"
