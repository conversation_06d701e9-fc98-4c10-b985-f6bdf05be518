<launch>
  <arg name="joy_dev" default="/dev/input/js0" />
  
  <node pkg="joy" type="joy_node" name="joy_node">
    <param name="dev" value="$(arg joy_dev)" />
    <param name="deadzone" value="0.3" />
    <param name="autorepeat_rate" value="20" />
  </node>

  <node name="follower" type="follower.py" pkg="tracker_pkg" output="screen">
    <param name='maxSpeed' value='0.4' type='double' />
    <param name='targetDist' value='2.0' type='double' />
    <param name="enable_button" value="4" />
    <rosparam ns='PID_controller' command='load' file='$(find tracker_pkg)/parameters/PID_param.yaml' />
  </node>
</launch>
