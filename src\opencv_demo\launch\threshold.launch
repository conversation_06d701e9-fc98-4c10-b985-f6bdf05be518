<launch>
  <arg name="node_name" default="threshold" />
  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="apply_otsu" default="false" doc="Decide applying Otsu's algotirhm threshold. If this value is true, threshold value will be ignored" />
  <arg name="threshold_type" default="0" doc="Specify threshold types. 0: Binary threshold, 1: Binary inverted threshold, 2: Threshold truncated, 3: Threshold to zero, 4: Threshold to zero inverted" />
  <arg name="threshold" default="150" doc="threshold value"/>
  <arg name="max_binary" default="255" doc="maximum value to use with the 0: Binary thresholding and 1: Binary inverted thresholding types."/>

  <!-- threshold.cpp -->
  <node name="$(arg node_name)"
        pkg="opencv_apps" type="threshold">
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="threshold_type" value="$(arg threshold_type)" />
    <param name="apply_otsu" value="$(arg apply_otsu)" />
    <param name="threshold" value="$(arg threshold)" />
    <param name="max_binary" value="$(arg max_binary)" />
  </node>

</launch>
