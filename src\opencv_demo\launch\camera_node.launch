<launch>
  <node pkg="uvc_camera" type="uvc_camera_node" name="uvc_camera" output="screen">
    <param name="width" type="int" value="320" />
    <param name="height" type="int" value="240" />
    <param name="fps" type="int" value="30" />
    <param name="frame" type="string" value="wide_stereo" />
    <remap from="/image_raw"  to="image"/>
    <param name="auto_focus" type="bool" value="False" />
    <param name="focus_absolute" type="int" value="0" />
    <!-- other supported params: auto_exposure, exposure_absolute, brightness, power_line_frequency -->

    <param name="device" type="string" value="/dev/video0" />
    <param name="camera_info_url" type="string" value="file://$(find uvc_camera)/example.yaml" />
  </node>
</launch>
