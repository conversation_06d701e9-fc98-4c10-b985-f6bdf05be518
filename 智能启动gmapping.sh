#!/bin/bash

###智能gmapping启动脚本 - 自动检测设备###

WORKSPACE_PATH="$HOME/310319"

echo "=== 智能Gmapping启动 ==="

# 检查并修复激光雷达设备
echo "🔍 检查激光雷达设备..."

# 自动检测串口设备
LIDAR_DEVICE=""
if [ -e "/dev/ttyUSB0" ]; then
    LIDAR_DEVICE="/dev/ttyUSB0"
    echo "✅ 找到激光雷达设备: /dev/ttyUSB0"
elif [ -e "/dev/ttyUSB1" ]; then
    LIDAR_DEVICE="/dev/ttyUSB1"
    echo "✅ 找到激光雷达设备: /dev/ttyUSB1"
elif [ -e "/dev/ttyACM0" ]; then
    LIDAR_DEVICE="/dev/ttyACM0"
    echo "✅ 找到激光雷达设备: /dev/ttyACM0"
else
    echo "❌ 未找到激光雷达设备"
    echo "请检查USB连接"
    exit 1
fi

# 修复设备权限
echo "🔧 修复设备权限..."
sudo chmod 666 $LIDAR_DEVICE
echo "✅ 权限修复完成"

# 创建rplidar符号链接（兼容原配置）
echo "🔗 创建设备链接..."
sudo rm -f /dev/rplidar 2>/dev/null
sudo ln -s $LIDAR_DEVICE /dev/rplidar
echo "✅ 已创建 /dev/rplidar -> $LIDAR_DEVICE"

# 检查工作空间
if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    echo "❌ 工作空间未编译: $WORKSPACE_PATH"
    exit 1
fi

echo "🚀 启动Gmapping系统..."

# 启动系统
gnome-terminal --window -e 'bash -c "echo \"[1/5] 启动roscore...\"; roscore; exec bash"' \
--tab -e "bash -c \"echo '[2/5] 等待roscore启动...'; sleep 5; echo '启动机器人硬件...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup robot_with_imu.launch serial_port:=$LIDAR_DEVICE; exec bash\"" \
--tab -e "bash -c \"echo '[3/5] 等待硬件启动...'; sleep 10; echo '启动gmapping算法...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '[4/5] 等待gmapping启动...'; sleep 15; echo '启动可视化界面...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '[5/5] 等待系统就绪...'; sleep 20; echo '启动键盘控制...'; echo '使用 i,j,k,l 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py; exec bash\""

echo "✅ 系统启动完成！"
echo ""
echo "📋 使用的设备: $LIDAR_DEVICE"
echo "🎮 键盘控制: i(前) k(后) j(左转) l(右转)"
