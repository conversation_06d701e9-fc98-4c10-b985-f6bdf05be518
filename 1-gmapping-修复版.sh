#!/bin/bash

###gmapping with abot - 修复版###

# 配置参数
WORKSPACE_PATH="$HOME/310319"
TERMINAL_TYPE="gnome-terminal"

echo "=== 启动Gmapping系统 ==="
echo "工作空间: $WORKSPACE_PATH"

# 检查工作空间是否存在
if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    echo "❌ 错误: 工作空间不存在或未编译"
    echo "请确保路径正确: $WORKSPACE_PATH"
    echo "或运行: cd $WORKSPACE_PATH && catkin_make"
    exit 1
fi

# 检查终端类型
if ! command -v $TERMINAL_TYPE &> /dev/null; then
    echo "❌ 错误: $TERMINAL_TYPE 未安装"
    echo "请安装: sudo apt-get install gnome-terminal"
    exit 1
fi

echo "✅ 环境检查通过，开始启动..."

# 启动gmapping系统
$TERMINAL_TYPE --window -e 'bash -c "echo \"[1/5] 启动roscore...\"; roscore; exec bash"' \
--tab -e "bash -c \"echo '[2/5] 等待roscore启动...'; sleep 5; echo '启动机器人硬件...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup robot_with_imu.launch; exec bash\"" \
--tab -e "bash -c \"echo '[3/5] 等待硬件启动...'; sleep 8; echo '启动gmapping算法...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '[4/5] 等待gmapping启动...'; sleep 12; echo '启动可视化界面...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '[5/5] 等待系统就绪...'; sleep 15; echo '启动键盘控制...'; echo '使用 i,j,k,l,, 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py; exec bash\""

echo "🚀 Gmapping系统已启动！"
echo ""
echo "📋 启动顺序："
echo "1. roscore (ROS核心)"
echo "2. robot_with_imu.launch (硬件驱动)"
echo "3. gmapping.launch (SLAM算法)"
echo "4. view_mapping.launch (可视化)"
echo "5. teleop_twist_keyboard (键盘控制)"
echo ""
echo "🎮 键盘控制说明："
echo "  i - 前进    k - 后退"
echo "  j - 左转    l - 右转"
echo "  , - 减速    . - 加速"
echo "  空格 - 停止"
echo ""
echo "💡 提示："
echo "- 请等待所有节点完全启动后再开始建图"
echo "- 建图时请缓慢移动机器人"
echo "- 按Ctrl+C可停止各个节点"
