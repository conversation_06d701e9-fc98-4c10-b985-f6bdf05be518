<launch>
  <arg name="node_name" default="face_detection" />
  <arg name="use_opencv3" default="false" />
  <arg name="use_opencv3_1" default="false" />
  <arg name="use_opencv3_2" default="false" />
  <arg name="use_opencv3_3" default="$(arg use_opencv3)" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg if="$(arg use_opencv3_1)"
       name="face_cascade_name" default="$(find opencv3)/../OpenCV-3.1.0-dev/haarcascades/haarcascade_frontalface_alt.xml" doc="Face dtection cascade Filename" />
  <arg if="$(arg use_opencv3_1)"
       name="eyes_cascade_name" default="$(find opencv3)/../OpenCV-3.1.0-dev/haarcascades/haarcascade_eye_tree_eyeglasses.xml" doc="Eye dtection cascade Filename" />
  <arg if="$(arg use_opencv3_2)"
       name="face_cascade_name" default="$(find opencv3)/../OpenCV-3.2.0-dev/haarcascades/haarcascade_frontalface_alt.xml" doc="Face dtection cascade Filename" />
  <arg if="$(arg use_opencv3_2)"
       name="eyes_cascade_name" default="$(find opencv3)/../OpenCV-3.2.0-dev/haarcascades/haarcascade_eye_tree_eyeglasses.xml" doc="Eye dtection cascade Filename" />
  <arg if="$(arg use_opencv3_3)"
       name="face_cascade_name" default="$(find opencv3)/../OpenCV-3.3.1-dev/haarcascades/haarcascade_frontalface_alt.xml" doc="Face dtection cascade Filename" />
  <arg if="$(arg use_opencv3_3)"
       name="eyes_cascade_name" default="$(find opencv3)/../OpenCV-3.3.1-dev/haarcascades/haarcascade_eye_tree_eyeglasses.xml" doc="Eye dtection cascade Filename" />
  <arg unless="$(arg use_opencv3)"
       name="face_cascade_name" default="/usr/share/opencv/haarcascades/haarcascade_frontalface_alt.xml" doc="Face dtection cascade Filename" />
  <arg unless="$(arg use_opencv3)"
       name="eyes_cascade_name" default="/usr/share/opencv/haarcascades/haarcascade_eye_tree_eyeglasses.xml" doc="Eye dtection cascade Filename" />

  <!-- face_detection.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="face_detection"  >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="face_cascade_name" value="$(arg face_cascade_name)" />
    <param name="eyes_cascade_name" value="$(arg eyes_cascade_name)" />
  </node>
</launch>
