#!/bin/bash

# RPLidar错误修复脚本
# 解决rplidarNode进程死亡问题

echo "=== RPLidar错误诊断和修复 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}步骤 $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step "1" "检查USB设备连接"
echo "查找RPLidar设备..."

# 检查USB设备
if lsusb | grep -i "cp210\|silicon\|rplidar" >/dev/null; then
    print_success "检测到可能的RPLidar USB设备"
    lsusb | grep -i "cp210\|silicon\|rplidar"
else
    print_warning "未检测到明显的RPLidar USB设备"
    echo "所有USB设备:"
    lsusb
fi
echo ""

print_step "2" "检查串口设备"
echo "查找串口设备..."

# 检查常见的串口设备
devices_found=false

for device in /dev/ttyUSB* /dev/ttyACM*; do
    if [ -e "$device" ]; then
        print_success "找到串口设备: $device"
        ls -l "$device"
        devices_found=true
    fi
done

if [ "$devices_found" = false ]; then
    print_error "未找到串口设备 (/dev/ttyUSB* 或 /dev/ttyACM*)"
    echo "可能原因:"
    echo "1. RPLidar未连接"
    echo "2. USB线缆故障"
    echo "3. 驱动程序未安装"
fi
echo ""

print_step "3" "检查设备权限"
echo "检查串口设备权限..."

for device in /dev/ttyUSB* /dev/ttyACM*; do
    if [ -e "$device" ]; then
        if [ -r "$device" ] && [ -w "$device" ]; then
            print_success "$device 权限正常"
        else
            print_error "$device 权限不足"
            echo "修复命令:"
            echo "sudo chmod 666 $device"
            echo "或永久修复:"
            echo "sudo usermod -a -G dialout \$USER"
            echo "然后重新登录"
        fi
    fi
done
echo ""

print_step "4" "检查RPLidar配置文件"
WORKSPACE_PATH="$HOME/310319"
RPLIDAR_LAUNCH="$WORKSPACE_PATH/src/abot_base/abot_bringup/launch/rplidar.launch"

if [ -f "$RPLIDAR_LAUNCH" ]; then
    print_success "RPLidar launch文件存在"
    echo "文件路径: $RPLIDAR_LAUNCH"
    echo ""
    echo "当前配置:"
    cat "$RPLIDAR_LAUNCH"
    echo ""
    
    # 检查配置中的串口设置
    if grep -q "/dev/rplidar" "$RPLIDAR_LAUNCH"; then
        print_warning "配置使用 /dev/rplidar，检查此设备是否存在"
        if [ -e "/dev/rplidar" ]; then
            print_success "/dev/rplidar 设备存在"
        else
            print_error "/dev/rplidar 设备不存在"
            echo "解决方案:"
            echo "1. 创建符号链接: sudo ln -s /dev/ttyUSB0 /dev/rplidar"
            echo "2. 或修改launch文件使用实际设备名"
        fi
    fi
else
    print_error "RPLidar launch文件不存在: $RPLIDAR_LAUNCH"
fi
echo ""

print_step "5" "测试RPLidar通信"
echo "尝试手动测试RPLidar..."

# 查找实际的串口设备
actual_device=""
for device in /dev/ttyUSB0 /dev/ttyUSB1 /dev/ttyACM0; do
    if [ -e "$device" ]; then
        actual_device="$device"
        break
    fi
done

if [ -n "$actual_device" ]; then
    print_success "使用设备 $actual_device 进行测试"
    echo "测试命令:"
    echo "rosrun rplidar_ros rplidarNode _serial_port:=$actual_device"
    echo ""
    echo "如果要立即测试，请在新终端中运行上述命令"
else
    print_error "未找到可用的串口设备进行测试"
fi
echo ""

print_step "6" "常见解决方案"
echo "根据错误代码255，以下是常见解决方案:"
echo ""

echo "解决方案1: 修复设备权限"
echo "sudo chmod 666 /dev/ttyUSB*"
echo "sudo usermod -a -G dialout \$USER"
echo "# 然后重新登录"
echo ""

echo "解决方案2: 创建设备符号链接"
echo "sudo ln -s /dev/ttyUSB0 /dev/rplidar"
echo "# 或根据实际设备调整"
echo ""

echo "解决方案3: 修改launch文件"
echo "编辑 $RPLIDAR_LAUNCH"
echo "将 <param name=\"serial_port\" value=\"/dev/rplidar\"/>"
echo "改为 <param name=\"serial_port\" value=\"/dev/ttyUSB0\"/>"
echo ""

echo "解决方案4: 检查USB连接"
echo "1. 重新插拔USB线"
echo "2. 尝试不同的USB端口"
echo "3. 检查USB线是否损坏"
echo ""

echo "解决方案5: 重启相关服务"
echo "sudo systemctl restart udev"
echo "sudo udevadm control --reload-rules"
echo ""

print_step "7" "自动修复尝试"
echo "是否尝试自动修复常见问题? (y/n)"
read -r response

if [ "$response" = "y" ] || [ "$response" = "Y" ]; then
    echo "开始自动修复..."
    
    # 修复权限
    echo "修复串口设备权限..."
    sudo chmod 666 /dev/ttyUSB* 2>/dev/null || echo "未找到ttyUSB设备"
    sudo chmod 666 /dev/ttyACM* 2>/dev/null || echo "未找到ttyACM设备"
    
    # 创建符号链接
    if [ -e "/dev/ttyUSB0" ] && [ ! -e "/dev/rplidar" ]; then
        echo "创建rplidar符号链接..."
        sudo ln -s /dev/ttyUSB0 /dev/rplidar
        print_success "已创建 /dev/rplidar -> /dev/ttyUSB0"
    fi
    
    # 重新加载udev规则
    echo "重新加载udev规则..."
    sudo udevadm control --reload-rules
    sudo udevadm trigger
    
    print_success "自动修复完成"
    echo ""
    echo "请重新启动gmapping系统测试"
else
    echo "跳过自动修复"
fi
echo ""

print_step "8" "验证修复"
echo "修复后验证步骤:"
echo "1. 检查设备: ls -l /dev/ttyUSB* /dev/rplidar"
echo "2. 测试权限: echo 'test' > /dev/ttyUSB0 (应该不报错)"
echo "3. 启动节点: rosrun rplidar_ros rplidarNode"
echo "4. 检查话题: rostopic list | grep scan"
echo "5. 查看数据: rostopic echo /scan"
echo ""

echo "=== 诊断完成 ==="
echo "如果问题仍然存在，请:"
echo "1. 检查RPLidar硬件是否正常"
echo "2. 确认RPLidar型号和驱动兼容性"
echo "3. 查看详细日志: cat /home/<USER>/.ros/log/*/rplidarNode-*.log"
