<launch>
  <!-- 简化版RPLidar配置 - 不包含滤波器 -->
  <arg name="serial_port" default="/dev/ttyUSB0" />
  <arg name="serial_baudrate" default="115200" />

  <node name="rplidarNode" pkg="rplidar_ros" type="rplidarNode" output="screen">
    <param name="serial_port" type="string" value="$(arg serial_port)"/>
    <param name="serial_baudrate" type="int" value="$(arg serial_baudrate)"/>
    <param name="frame_id" type="string" value="laser_link"/>
    <param name="inverted" type="bool" value="false"/>
    <param name="angle_compensate" type="bool" value="true"/>
  </node>
  
  <!-- 暂时注释掉滤波器 -->
  <!-- <include file="$(find lidar_filters)/launch/box_filter_example.launch"/> -->
  
</launch>
