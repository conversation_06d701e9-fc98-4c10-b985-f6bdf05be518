#!/bin/bash

###gmapping简化版 - 不需要IMU###

WORKSPACE_PATH="$HOME/310319"

echo "=== 启动简化版Gmapping（无IMU） ==="
echo "工作空间: $WORKSPACE_PATH"

# 检查工作空间
if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    echo "❌ 工作空间未编译，请先运行: cd $WORKSPACE_PATH && catkin_make"
    exit 1
fi

echo "启动简化版gmapping系统..."

# 使用gnome-terminal启动多个标签页
gnome-terminal --window -e 'bash -c "echo \"启动roscore...\"; roscore; exec bash"' \
--tab -e "bash -c \"echo '等待roscore启动...'; sleep 5; echo '启动激光雷达...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup rplidar.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待激光雷达启动...'; sleep 8; echo '启动底盘驱动...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup bringup.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待底盘启动...'; sleep 10; echo '启动机器人模型...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup model.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待模型加载...'; sleep 12; echo '启动gmapping...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待gmapping启动...'; sleep 15; echo '启动RViz...'; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch; exec bash\"" \
--tab -e "bash -c \"echo '等待系统就绪...'; sleep 18; echo '启动键盘控制...'; echo '使用 i,j,k,l,, 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py; exec bash\""

echo "简化版gmapping已启动！"
echo ""
echo "注意："
echo "1. 此版本不使用IMU，定位精度可能较低"
echo "2. 如果激光雷达还有问题，请先解决激光雷达连接"
echo "3. 建图时请缓慢移动机器人"
