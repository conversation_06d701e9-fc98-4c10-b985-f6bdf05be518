@echo off
REM Gmapping调试脚本 - Windows版本
REM 用于在Windows环境下检查ROS gmapping配置

echo === ROS Gmapping 调试脚本 (Windows版) ===
echo 开始检查ROS环境和依赖...
echo.

REM 检查是否在WSL环境中
echo 1. 检查运行环境...
if exist "C:\Windows\System32\wsl.exe" (
    echo ✅ 检测到WSL，建议在WSL中运行ROS
    echo    使用命令: wsl
    echo    然后在WSL中运行Linux版本的调试脚本
) else (
    echo ⚠️  未检测到WSL，ROS通常需要Linux环境
)
echo.

REM 检查工作空间目录
echo 2. 检查工作空间...
set WORKSPACE_PATH=F:\310319
if exist "%WORKSPACE_PATH%" (
    echo ✅ 工作空间路径存在: %WORKSPACE_PATH%
) else (
    echo ❌ 错误: 工作空间路径不存在: %WORKSPACE_PATH%
)
echo.

REM 检查关键文件
echo 3. 检查关键文件...
if exist "%WORKSPACE_PATH%\1-gmapping.sh" (
    echo ✅ gmapping启动脚本存在
) else (
    echo ❌ 错误: 1-gmapping.sh文件不存在
)

if exist "%WORKSPACE_PATH%\src\robot_slam\launch\gmapping.launch" (
    echo ✅ gmapping.launch文件存在
) else (
    echo ❌ 错误: gmapping.launch文件不存在
)

if exist "%WORKSPACE_PATH%\devel" (
    echo ✅ devel目录存在(工作空间已编译)
) else (
    echo ❌ 错误: devel目录不存在(工作空间未编译)
)
echo.

REM 显示文件内容检查
echo 4. 检查gmapping脚本内容...
if exist "%WORKSPACE_PATH%\1-gmapping.sh" (
    echo 脚本内容:
    type "%WORKSPACE_PATH%\1-gmapping.sh"
    echo.
    echo 分析:
    echo - 脚本使用gnome-terminal启动多个标签页
    echo - 需要Ubuntu/Linux环境运行
    echo - 依赖ROS melodic/noetic环境
)
echo.

echo 5. Windows环境下的建议...
echo =====================================
echo 由于ROS主要在Linux环境下运行，建议:
echo.
echo 选项1: 使用WSL2 (推荐)
echo   1. 安装WSL2: wsl --install
echo   2. 安装Ubuntu: wsl --install -d Ubuntu
echo   3. 在WSL中安装ROS
echo   4. 将代码复制到WSL环境
echo.
echo 选项2: 使用虚拟机
echo   1. 安装VirtualBox或VMware
echo   2. 创建Ubuntu虚拟机
echo   3. 在虚拟机中安装ROS
echo.
echo 选项3: 使用Docker
echo   1. 安装Docker Desktop
echo   2. 使用ROS Docker镜像
echo   3. 挂载代码目录
echo.

echo 6. 如果已在WSL/Linux环境中...
echo ================================
echo 请运行以下命令进行详细检查:
echo.
echo   cd /mnt/f/310319  # 或对应的Linux路径
echo   ./debug_gmapping.sh
echo   ./diagnose_gmapping_issues.sh
echo   ./step_by_step_gmapping.sh
echo.

echo 7. 常见问题预检查...
echo ===================
echo 问题1: 路径问题
echo   Windows路径: F:\310319
echo   WSL路径: /mnt/f/310319
echo   确保在正确环境中使用正确路径
echo.
echo 问题2: 权限问题
echo   在WSL中可能需要: chmod +x *.sh
echo.
echo 问题3: 行结束符问题
echo   Windows文件可能有CRLF，需要转换为LF
echo   使用: dos2unix *.sh
echo.

echo === 调试完成 ===
echo 建议在Linux环境(WSL/虚拟机)中运行ROS
pause
