# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from find_object_2d/DetectionInfo.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class DetectionInfo(genpy.Message):
  _md5sum = "c344e94bd36ff0758e87d95453ebd0bd"
  _type = "find_object_2d/DetectionInfo"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """
Header header

# All arrays should have the same size
std_msgs/Int32[] ids
std_msgs/Int32[] widths
std_msgs/Int32[] heights
std_msgs/String[] filePaths
std_msgs/Int32[] inliers
std_msgs/Int32[] outliers
# 3x3 homography matrix: [h11, h12, h13, h21, h22, h23, h31, h32, h33] (h31 = dx and h32 = dy, see QTransform)
std_msgs/Float32MultiArray[] homographies

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: std_msgs/Int32
int32 data
================================================================================
MSG: std_msgs/String
string data

================================================================================
MSG: std_msgs/Float32MultiArray
# Please look at the MultiArrayLayout message definition for
# documentation on all multiarrays.

MultiArrayLayout  layout        # specification of data layout
float32[]         data          # array of data


================================================================================
MSG: std_msgs/MultiArrayLayout
# The multiarray declares a generic multi-dimensional array of a
# particular data type.  Dimensions are ordered from outer most
# to inner most.

MultiArrayDimension[] dim # Array of dimension properties
uint32 data_offset        # padding elements at front of data

# Accessors should ALWAYS be written in terms of dimension stride
# and specified outer-most dimension first.
# 
# multiarray(i,j,k) = data[data_offset + dim_stride[1]*i + dim_stride[2]*j + k]
#
# A standard, 3-channel 640x480 image with interleaved color channels
# would be specified as:
#
# dim[0].label  = "height"
# dim[0].size   = 480
# dim[0].stride = 3*640*480 = 921600  (note dim[0] stride is just size of image)
# dim[1].label  = "width"
# dim[1].size   = 640
# dim[1].stride = 3*640 = 1920
# dim[2].label  = "channel"
# dim[2].size   = 3
# dim[2].stride = 3
#
# multiarray(i,j,k) refers to the ith row, jth column, and kth channel.

================================================================================
MSG: std_msgs/MultiArrayDimension
string label   # label of given dimension
uint32 size    # size of given dimension (in type units)
uint32 stride  # stride of given dimension"""
  __slots__ = ['header','ids','widths','heights','filePaths','inliers','outliers','homographies']
  _slot_types = ['std_msgs/Header','std_msgs/Int32[]','std_msgs/Int32[]','std_msgs/Int32[]','std_msgs/String[]','std_msgs/Int32[]','std_msgs/Int32[]','std_msgs/Float32MultiArray[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,ids,widths,heights,filePaths,inliers,outliers,homographies

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(DetectionInfo, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.ids is None:
        self.ids = []
      if self.widths is None:
        self.widths = []
      if self.heights is None:
        self.heights = []
      if self.filePaths is None:
        self.filePaths = []
      if self.inliers is None:
        self.inliers = []
      if self.outliers is None:
        self.outliers = []
      if self.homographies is None:
        self.homographies = []
    else:
      self.header = std_msgs.msg.Header()
      self.ids = []
      self.widths = []
      self.heights = []
      self.filePaths = []
      self.inliers = []
      self.outliers = []
      self.homographies = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.ids)
      buff.write(_struct_I.pack(length))
      for val1 in self.ids:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.widths)
      buff.write(_struct_I.pack(length))
      for val1 in self.widths:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.heights)
      buff.write(_struct_I.pack(length))
      for val1 in self.heights:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.filePaths)
      buff.write(_struct_I.pack(length))
      for val1 in self.filePaths:
        _x = val1.data
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.inliers)
      buff.write(_struct_I.pack(length))
      for val1 in self.inliers:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.outliers)
      buff.write(_struct_I.pack(length))
      for val1 in self.outliers:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.homographies)
      buff.write(_struct_I.pack(length))
      for val1 in self.homographies:
        _v1 = val1.layout
        length = len(_v1.dim)
        buff.write(_struct_I.pack(length))
        for val3 in _v1.dim:
          _x = val3.label
          length = len(_x)
          if python3 or type(_x) == unicode:
            _x = _x.encode('utf-8')
            length = len(_x)
          buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
          _x = val3
          buff.write(_get_struct_2I().pack(_x.size, _x.stride))
        _x = _v1.data_offset
        buff.write(_get_struct_I().pack(_x))
        length = len(val1.data)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(struct.Struct(pattern).pack(*val1.data))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.ids is None:
        self.ids = None
      if self.widths is None:
        self.widths = None
      if self.heights is None:
        self.heights = None
      if self.filePaths is None:
        self.filePaths = None
      if self.inliers is None:
        self.inliers = None
      if self.outliers is None:
        self.outliers = None
      if self.homographies is None:
        self.homographies = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.ids = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.ids.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.widths = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.widths.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.heights = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.heights.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.filePaths = []
      for i in range(0, length):
        val1 = std_msgs.msg.String()
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1.data = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1.data = str[start:end]
        self.filePaths.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.inliers = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.inliers.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.outliers = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.outliers.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.homographies = []
      for i in range(0, length):
        val1 = std_msgs.msg.Float32MultiArray()
        _v2 = val1.layout
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        _v2.dim = []
        for i in range(0, length):
          val3 = std_msgs.msg.MultiArrayDimension()
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            val3.label = str[start:end].decode('utf-8', 'rosmsg')
          else:
            val3.label = str[start:end]
          _x = val3
          start = end
          end += 8
          (_x.size, _x.stride,) = _get_struct_2I().unpack(str[start:end])
          _v2.dim.append(val3)
        start = end
        end += 4
        (_v2.data_offset,) = _get_struct_I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.data = s.unpack(str[start:end])
        self.homographies.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.ids)
      buff.write(_struct_I.pack(length))
      for val1 in self.ids:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.widths)
      buff.write(_struct_I.pack(length))
      for val1 in self.widths:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.heights)
      buff.write(_struct_I.pack(length))
      for val1 in self.heights:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.filePaths)
      buff.write(_struct_I.pack(length))
      for val1 in self.filePaths:
        _x = val1.data
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.inliers)
      buff.write(_struct_I.pack(length))
      for val1 in self.inliers:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.outliers)
      buff.write(_struct_I.pack(length))
      for val1 in self.outliers:
        _x = val1.data
        buff.write(_get_struct_i().pack(_x))
      length = len(self.homographies)
      buff.write(_struct_I.pack(length))
      for val1 in self.homographies:
        _v3 = val1.layout
        length = len(_v3.dim)
        buff.write(_struct_I.pack(length))
        for val3 in _v3.dim:
          _x = val3.label
          length = len(_x)
          if python3 or type(_x) == unicode:
            _x = _x.encode('utf-8')
            length = len(_x)
          buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
          _x = val3
          buff.write(_get_struct_2I().pack(_x.size, _x.stride))
        _x = _v3.data_offset
        buff.write(_get_struct_I().pack(_x))
        length = len(val1.data)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(val1.data.tostring())
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.ids is None:
        self.ids = None
      if self.widths is None:
        self.widths = None
      if self.heights is None:
        self.heights = None
      if self.filePaths is None:
        self.filePaths = None
      if self.inliers is None:
        self.inliers = None
      if self.outliers is None:
        self.outliers = None
      if self.homographies is None:
        self.homographies = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.ids = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.ids.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.widths = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.widths.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.heights = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.heights.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.filePaths = []
      for i in range(0, length):
        val1 = std_msgs.msg.String()
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1.data = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1.data = str[start:end]
        self.filePaths.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.inliers = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.inliers.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.outliers = []
      for i in range(0, length):
        val1 = std_msgs.msg.Int32()
        start = end
        end += 4
        (val1.data,) = _get_struct_i().unpack(str[start:end])
        self.outliers.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.homographies = []
      for i in range(0, length):
        val1 = std_msgs.msg.Float32MultiArray()
        _v4 = val1.layout
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        _v4.dim = []
        for i in range(0, length):
          val3 = std_msgs.msg.MultiArrayDimension()
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            val3.label = str[start:end].decode('utf-8', 'rosmsg')
          else:
            val3.label = str[start:end]
          _x = val3
          start = end
          end += 8
          (_x.size, _x.stride,) = _get_struct_2I().unpack(str[start:end])
          _v4.dim.append(val3)
        start = end
        end += 4
        (_v4.data_offset,) = _get_struct_I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.data = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
        self.homographies.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2I = None
def _get_struct_2I():
    global _struct_2I
    if _struct_2I is None:
        _struct_2I = struct.Struct("<2I")
    return _struct_2I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_i = None
def _get_struct_i():
    global _struct_i
    if _struct_i is None:
        _struct_i = struct.Struct("<i")
    return _struct_i
