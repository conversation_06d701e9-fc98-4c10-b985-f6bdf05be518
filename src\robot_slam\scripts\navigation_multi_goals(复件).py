#!/usr/bin/env python

#coding: utf-8

import rospy

import actionlib
from actionlib_msgs.msg import *
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseWithCovarianceStamped
from tf_conversions import transformations
from math import pi
from std_msgs.msg import String
from geometry_msgs.msg import Twist
from ar_track_alvar_msgs.msg import AlvarMarkers
from ar_track_alvar_msgs.msg import AlvarMarker

from geometry_msgs.msg  import Point
import sys
reload(sys)
sys.setdefaultencoding('utf-8')
import os

kaishi="~/abot_ws/src/robot_voice/kaishi.mp3"
yi="~/abot_ws/src/robot_voice/shibie1.mp3"
er="~/abot_ws/src/robot_voice/shibie2.mp3"
san="~/abot_ws/src/robot_voice/shibie3.mp3"
si="~/abot_ws/src/robot_voice/shibie4.mp3"
wu="~/abot_ws/src/robot_voice/shibie5.mp3"
liu="~/abot_ws/src/robot_voice/shibie6.mp3"
qi="~/abot_ws/src/robot_voice/shibie7.mp3"
ba="~/abot_ws/src/robot_voice/shibie8.mp3"
tyi="~/abot_ws/src/robot_voice/dao1.mp3"
ter="~/abot_ws/src/robot_voice/dao2.mp3"
tsan="~/abot_ws/src/robot_voice/dao3.mp3"
tsi="~/abot_ws/src/robot_voice/dao4.mp3"
twu="~/abot_ws/src/robot_voice/dao5.mp3"
tliu="~/abot_ws/src/robot_voice/dao6.mp3"
tqi="~/abot_ws/src/robot_voice/dao7.mp3"
tba="~/abot_ws/src/robot_voice/dao8.mp3"
zd="~/abot_ws/src/robot_voice/dao9.mp3"



id = 255
move_flog = 1

class move_robot2:
    def __init__(self):
        self.pub=rospy.Publisher("/cmd_vel",Twist,queue_size=1000)
    def move_cb(self):
        global time
        time=0
        msg=Twist()
        msg.linear.x=0.120
        msg.linear.y=0
        msg.linear.z=0
        msg.angular.x=0.0
        msg.angular.y=0.0
        msg.angular.z=0.0

        while time<41:
            self.pub.publish(msg)
            rospy.sleep(0.1)
            time+=1

class move_robot1:
    def __init__(self):
        self.pub=rospy.Publisher("/cmd_vel",Twist,queue_size=1000)
    def move_cb(self):
        global time
        time=0
        msg=Twist()
        msg.linear.x=0
        msg.linear.y=0.0900
        msg.linear.z=0
        msg.angular.x=0.0
        msg.angular.y=0.0
        msg.angular.z=0.0

        while time<45:
            self.pub.publish(msg)
            rospy.sleep(0.1)
            time+=1


class navigation_demo:
    def __init__(self):
        self.set_pose_pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=5)
        self.arrive_pub = rospy.Publisher('/voiceWords',String,queue_size=10)
        self.ar_sub = rospy.Subscriber('/ar_pose_marker', AlvarMarkers, self.ar_cb_1);
        self.ar_sub = rospy.Subscriber('/object_position', Point, self.ar_cb_2);
        self.move_base = actionlib.SimpleActionClient("move_base", MoveBaseAction)
        self.move_base.wait_for_server(rospy.Duration(60))


    def ar_cb_1(self, data):
        global id  
        ar_markers = data
        if (len(ar_markers.markers) == 1):
            ar_marker = ar_markers.markers[0]
            id = ar_marker.id

    def ar_cb_2(self, data):
        global id  
        global move_flog
        id =255
        point_msg = data
        #rospy.loginfo('z = %d', point_msg.z)
	if (point_msg.z != 255  and move_flog == 1) :
            if(point_msg.z>=74 and point_msg.z<=136 or point_msg.z>=1 and point_msg.z<=10 or  point_msg.z>=254 and point_msg.z<=257):
                id = 9
            elif(point_msg.z>=11 and point_msg.z<=20 or point_msg.z>=137 and point_msg.z<=143):
                id = 10
            elif(point_msg.z>=21 and point_msg.z<=30):
                id = 11
            elif(point_msg.z>=31 and point_msg.z<=40):
                id = 12
            elif(point_msg.z>=41 and point_msg.z<=50 or point_msg.z>=144 and point_msg.z<=171 or point_msg.z>=201 and point_msg.z<=216 or point_msg.z>=220 and point_msg.z<=253):
                id = 13
            elif(point_msg.z>=51 and point_msg.z<=60 or point_msg.z>=172 and point_msg.z<=195 or point_msg.z>=198 and point_msg.z<=200 or  point_msg.z>=217 and point_msg.z<=219):
                id = 14
            elif(point_msg.z>=61 and point_msg.z<=70):
                id = 15
            elif(point_msg.z>=71 and point_msg.z<=73 or point_msg.z>=196 and point_msg.z<=197):
                id = 16
        #print flog0 , flog1 , flog2
        #rospy.loginfo('id = %d', id)
    def set_pose(self, p):
        if self.move_base is None:
            return False

        x, y, th = p

        pose = PoseWithCovarianceStamped()
        pose.header.stamp = rospy.Time.now()
        pose.header.frame_id = 'map'
        pose.pose.pose.position.x = x
        pose.pose.pose.position.y = y
        q = transformations.quaternion_from_euler(0.0, 0.0, th/180.0*pi)
        pose.pose.pose.orientation.x = q[0]
        pose.pose.pose.orientation.y = q[1]
        pose.pose.pose.orientation.z = q[2]
        pose.pose.pose.orientation.w = q[3]

        self.set_pose_pub.publish(pose)
        return True

    def _done_cb(self, status, result):
        rospy.loginfo("navigation done! status:%d result:%s"%(status, result))
        arrive_str = "arrived to traget point"
        self.arrive_pub.publish(arrive_str)

    def _active_cb(self):
        rospy.loginfo("[Navi] navigation has be actived")

    

    def _feedback_cb(self, feedback):
        msg = feedback
        #rospy.loginfo("[Navi] navigation feedback\r\n%s"%feedback)


    def goto(self, p):
        rospy.loginfo("[Navi] goto %s"%p)
        #arrive_str = "going to next point"
        #self.arrive_pub.publish(arrive_str)
        goal = MoveBaseGoal()

        goal.target_pose.header.frame_id = 'map'
        goal.target_pose.header.stamp = rospy.Time.now()
        goal.target_pose.pose.position.x = p[0]
        goal.target_pose.pose.position.y = p[1]
        q = transformations.quaternion_from_euler(0.0, 0.0, p[2]/180.0*pi)
        goal.target_pose.pose.orientation.x = q[0]
        goal.target_pose.pose.orientation.y = q[1]
        goal.target_pose.pose.orientation.z = q[2]
        goal.target_pose.pose.orientation.w = q[3]

        self.move_base.send_goal(goal, self._done_cb, self._active_cb, self._feedback_cb)
        result = self.move_base.wait_for_result(rospy.Duration(60))
        if not result:
            self.move_base.cancel_goal()
            rospy.loginfo("Timed out achieving goal")
        else:
            state = self.move_base.get_state()
            if state == GoalStatus.SUCCEEDED:
                rospy.loginfo("reach goal %s succeeded!"%p)
        return True

    def cancel(self):
        self.move_base.cancel_all_goals()
        return True
if __name__ == "__main__":
    rospy.init_node('navigation_demo',anonymous=True)
    goalListX = rospy.get_param('~goalListX', '2.0, 2.0')
    goalListY = rospy.get_param('~goalListY', '2.0, 4.0')
    goalListYaw = rospy.get_param('~goalListYaw', '0, 90.0')

    goals = [[float(x), float(y), float(yaw)] for (x, y, yaw) in zip(goalListX.split(","),goalListY.split(","),goalListYaw.split(","))]
    print ('Please 1 to continue: ')
    input = raw_input()
    print (goals)
    r = rospy.Rate(1)
    r.sleep()
    navi = navigation_demo()
    if (input == '1'):
            #os.system('mplayer %s' % music_path)
	os.system('mplayer %s' % kaishi)   
        navi.goto(goals[9])#sb
        rospy.sleep(8)
        if (id == 1 or id == 9):
	    os.system('mplayer %s' % yi)
            #navi.goto(goals[13])
            navi.goto(goals[1])#rw
	    os.system('mplayer %s' % tyi)
        if (id == 2 or id==10):
	    os.system('mplayer %s' % er)
            navi.goto(goals[2])#rw
	    os.system('mplayer %s' % ter) 
	navi.goto(goals[14])
        rospy.sleep(0.5)
        navi.goto(goals[10])#sb
        rospy.sleep(10)
        if (id == 3 or id == 11):
	    os.system('mplayer %s' % san)
            navi.goto(goals[3])
	    os.system('mplayer %s' % tsan)
        if (id == 4 or id==12):
	    os.system('mplayer %s' % si)
            #navi.goto(goals[14])
            navi.goto(goals[4])
	    os.system('mplayer %s' % tsi)
	navi.goto(goals[15])
        rospy.sleep(0.5)
        navi.goto(goals[11])#sb
        rospy.sleep(10)
        if (id == 5 or id == 13):
	    os.system('mplayer %s' % wu)
            #navi.goto(goals[15])
            navi.goto(goals[5])
	    os.system('mplayer %s' % twu)
        if (id == 6 or id==14):
	    os.system('mplayer %s' % liu)
            navi.goto(goals[6])
	    os.system('mplayer %s' % tliu) 
	navi.goto(goals[16])
        rospy.sleep(0.5)
        navi.goto(goals[12])#sb
        rospy.sleep(9)
        if (id == 7 or id == 15):
	    os.system('mplayer %s' % qi)
            navi.goto(goals[7])
	    os.system('mplayer %s' % tqi)
        if (id == 8 or id == 16):
	    os.system('mplayer %s' % ba)
            #navi.goto(goals[16])   
            navi.goto(goals[8])
	    os.system('mplayer %s' % tba)
        rospy.sleep(1)
	navi.goto(goals[17])
        #navi.goto(goals[8])
	move=move_robot2()
        move.move_cb()
	rospy.sleep(1)
	move=move_robot1()
        move.move_cb()
	os.system('mplayer %s' % zd)
    while not rospy.is_shutdown():
          r.sleep()
