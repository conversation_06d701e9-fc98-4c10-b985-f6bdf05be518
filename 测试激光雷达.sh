#!/bin/bash

echo "=== 测试激光雷达连接 ==="
echo ""

# 检查USB设备
echo "1. 检查USB设备："
lsusb | grep -i "cp210\|silicon\|prolific\|ftdi" || echo "未找到常见的USB转串口芯片"
echo ""

# 检查串口设备
echo "2. 检查串口设备："
ls /dev/ttyUSB* /dev/ttyACM* 2>/dev/null || echo "未找到串口设备"
echo ""

# 测试不同的串口和波特率
echo "3. 测试激光雷达通信："
echo ""

for device in /dev/ttyUSB0 /dev/ttyUSB1 /dev/ttyACM0; do
    if [ -e "$device" ]; then
        echo "测试设备: $device"
        
        # 测试115200波特率
        echo "  测试115200波特率..."
        timeout 5 rosrun rplidar_ros rplidarNode _serial_port:=$device _serial_baudrate:=115200 2>/dev/null &
        sleep 3
        pkill -f rplidarNode
        
        # 测试256000波特率
        echo "  测试256000波特率..."
        timeout 5 rosrun rplidar_ros rplidarNode _serial_port:=$device _serial_baudrate:=256000 2>/dev/null &
        sleep 3
        pkill -f rplidarNode
        
        echo ""
    fi
done

echo "4. 手动测试方法："
echo "在新终端中运行以下命令测试："
echo ""

for device in /dev/ttyUSB0 /dev/ttyUSB1; do
    if [ -e "$device" ]; then
        echo "# 测试 $device"
        echo "rosrun rplidar_ros rplidarNode _serial_port:=$device _serial_baudrate:=115200"
        echo "# 或者"
        echo "rosrun rplidar_ros rplidarNode _serial_port:=$device _serial_baudrate:=256000"
        echo ""
    fi
done

echo "5. 如果上面都不行，试试这些："
echo "# 重置USB设备"
echo "sudo rmmod cp210x && sudo modprobe cp210x"
echo ""
echo "# 或者重启USB子系统"
echo "sudo systemctl restart udev"
echo ""

echo "=== 测试完成 ==="
echo "如果激光雷达正常，应该能看到类似这样的输出："
echo "[ INFO] RPLIDAR running on ROS package rplidar_ros"
echo "[ INFO] SDK Version: 1.x.x"
echo "[ INFO] RPLIDAR health status : OK"
