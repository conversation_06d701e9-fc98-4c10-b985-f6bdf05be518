<launch>
  <arg name="launch_face_detection" default="true" />
  <arg name="launch_trainer" default="true" />

  <arg name="use_opencv3" default="false" />
  <arg name="use_opencv3_1" default="false" />
  <arg name="use_opencv3_2" default="false" />
  <arg name="use_opencv3_3" default="false" />
  <arg name="debug_view" default="true" />
  <arg name="queue_size" default="100" doc="Specigy queue_size of input image subscribers" />

  <arg name="image" default="image" />
  <arg name="data_dir" default="~/abot_vision/src/opencv_demo//date" />

  <include file="$(find opencv_apps)/launch/face_detection.launch"
           if="$(arg launch_face_detection)">
    <arg name="image" value="$(arg image)" />
    <arg name="debug_view" value="$(arg debug_view)" />
    <arg name="node_name" value="face_detection" />
    <arg name="use_opencv3" value="$(arg use_opencv3)" />
    <arg name="use_opencv3_1" value="$(arg use_opencv3_1)" />
    <arg name="use_opencv3_2" value="$(arg use_opencv3_2)" />
    <arg name="use_opencv3_3" value="$(arg use_opencv3_3)" />
  </include>

  <node name="face_recognition" pkg="opencv_apps" type="face_recognition"
        output="screen">
    <param name="data_dir" value="$(arg data_dir)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <remap from="image" to="$(arg image)" />
    <remap from="faces" to="face_detection/faces" />
  </node>

  <node name="face_recognition_trainer" pkg="opencv_apps" type="face_recognition_trainer.py"
        if="$(arg launch_trainer)" launch-prefix="xterm -fn 12x24 -e" respawn="true">
    <remap from="image" to="$(arg image)" />
    <remap from="faces" to="face_detection/faces" />
    <remap from="train" to="face_recognition/train" />
  </node>

  <node name="$(anon debug_image_viewer)" pkg="image_view" type="image_view"
        if="$(arg debug_view)">
    <remap from="image" to="face_recognition/debug_image" />
  </node>
</launch>
