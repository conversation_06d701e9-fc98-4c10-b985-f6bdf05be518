set(_CATKIN_CURRENT_PACKAGE "abot_imu")
set(abot_imu_VERSION "0.0.0")
set(abot_imu_MAINTAINER "abot <<EMAIL>>")
set(abot_imu_PACKAGE_FORMAT "2")
set(abot_imu_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_generation")
set(abot_imu_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(abot_imu_BUILDTOOL_DEPENDS "catkin")
set(abot_imu_BUILDTOOL_EXPORT_DEPENDS )
set(abot_imu_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_runtime")
set(abot_imu_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_runtime")
set(abot_imu_TEST_DEPENDS )
set(abot_imu_DOC_DEPENDS )
set(abot_imu_URL_WEBSITE "")
set(abot_imu_URL_BUGTRACKER "")
set(abot_imu_URL_REPOSITORY "")
set(abot_imu_DEPRECATED "")