<launch>
  <arg name="node_name" default="hough_lines" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="hough_type" default="0" doc="Apecify Hough transform type, 0 for Standard Hough Detection and 1 for Probabilistic Hough Detection" />
  <arg name="threshold" default="150" doc="Threshould value for hough line detection" />
  <arg name="rho" default="1.0" doc="The resolution of the parameter r in pixels." />
  <arg name="theta" default="1.0" doc="The resolution of the parameter theta in radians." />
  <arg name="minLineLength" default="30" doc="The minimum number of points that can form a line." />
  <arg name="maxLineGrap" default="10" doc="The maximum gap between two points to be considered in the same line." />

  <!-- hough_lines.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="hough_lines" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="hough_type" value="$(arg hough_type)" />
    <param name="threshold" value="$(arg threshold)" />
    <param name="rho" value="$(arg rho)" />
    <param name="theta" value="$(arg theta)" />
    <param name="minLineLength" value="$(arg minLineLength)" />
    <param name="maxLineGrap" value="$(arg maxLineGrap)" />
  </node>
</launch>
