#!/bin/bash

# Gmapping问题诊断脚本
# 帮助识别和解决常见的gmapping执行问题

echo "=== Gmapping问题诊断脚本 ==="
echo "正在检查可能的问题..."
echo ""

WORKSPACE_PATH="$HOME/310319"
LOG_FILE="/tmp/gmapping_diagnosis.log"

# 创建日志文件
echo "诊断开始时间: $(date)" > $LOG_FILE

# 1. 基础环境检查
echo "1. 基础环境检查"
echo "=================="

# ROS版本检查
if [ -z "$ROS_DISTRO" ]; then
    echo "❌ 问题: ROS环境未设置"
    echo "解决方案:"
    echo "  source /opt/ros/melodic/setup.bash  # 或noetic"
    echo "  echo 'source /opt/ros/melodic/setup.bash' >> ~/.bashrc"
    echo ""
else
    echo "✅ ROS版本: $ROS_DISTRO"
fi

# Python版本检查(对于不同ROS版本)
python_version=$(python --version 2>&1)
echo "Python版本: $python_version"
if [[ "$ROS_DISTRO" == "melodic" ]] && [[ "$python_version" != *"2.7"* ]]; then
    echo "⚠️  警告: ROS Melodic需要Python 2.7"
elif [[ "$ROS_DISTRO" == "noetic" ]] && [[ "$python_version" != *"3."* ]]; then
    echo "⚠️  警告: ROS Noetic需要Python 3.x"
fi
echo ""

# 2. 工作空间检查
echo "2. 工作空间检查"
echo "================"

if [ ! -d "$WORKSPACE_PATH" ]; then
    echo "❌ 问题: 工作空间目录不存在: $WORKSPACE_PATH"
    echo "解决方案: 检查路径或修改脚本中的WORKSPACE_PATH"
    echo ""
else
    echo "✅ 工作空间存在: $WORKSPACE_PATH"
    
    # 检查编译状态
    if [ ! -d "$WORKSPACE_PATH/devel" ]; then
        echo "❌ 问题: 工作空间未编译(devel目录不存在)"
        echo "解决方案:"
        echo "  cd $WORKSPACE_PATH"
        echo "  catkin_make"
        echo ""
    else
        echo "✅ 工作空间已编译"
        
        # 检查setup.bash
        if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
            echo "❌ 问题: setup.bash文件不存在"
            echo "解决方案: 重新编译工作空间"
            echo ""
        else
            echo "✅ setup.bash文件存在"
            source "$WORKSPACE_PATH/devel/setup.bash"
        fi
    fi
fi

# 3. 依赖包检查
echo "3. 依赖包检查"
echo "=============="

packages=("gmapping" "teleop_twist_keyboard" "rviz" "tf" "sensor_msgs" "nav_msgs")
for pkg in "${packages[@]}"; do
    if rospack find $pkg >/dev/null 2>&1; then
        echo "✅ $pkg 包已安装"
    else
        echo "❌ 问题: $pkg 包未安装"
        echo "解决方案: sudo apt-get install ros-$ROS_DISTRO-$pkg"
        echo ""
    fi
done

# 4. 自定义包检查
echo "4. 自定义包检查"
echo "================"

custom_packages=("abot_bringup" "robot_slam")
for pkg in "${custom_packages[@]}"; do
    if rospack find $pkg >/dev/null 2>&1; then
        echo "✅ $pkg 包存在"
    else
        echo "❌ 问题: $pkg 包不存在或未正确编译"
        echo "检查路径: $WORKSPACE_PATH/src/"
        echo ""
    fi
done

# 5. Launch文件检查
echo "5. Launch文件检查"
echo "=================="

launch_files=(
    "abot_bringup robot_with_imu.launch"
    "robot_slam gmapping.launch"
    "robot_slam view_mapping.launch"
)

for launch_info in "${launch_files[@]}"; do
    pkg=$(echo $launch_info | cut -d' ' -f1)
    file=$(echo $launch_info | cut -d' ' -f2)
    
    if rospack find $pkg >/dev/null 2>&1; then
        pkg_path=$(rospack find $pkg)
        launch_path="$pkg_path/launch/$file"
        if [ -f "$launch_path" ]; then
            echo "✅ $pkg/$file 存在"
        else
            echo "❌ 问题: $pkg/$file 不存在"
            echo "路径: $launch_path"
            echo ""
        fi
    else
        echo "❌ 问题: 包 $pkg 不存在，无法检查 $file"
        echo ""
    fi
done

# 6. 硬件设备检查
echo "6. 硬件设备检查"
echo "================"

# USB设备检查
echo "USB设备列表:"
lsusb 2>/dev/null || echo "lsusb命令不可用"

# 串口设备检查
echo "串口设备:"
ls /dev/ttyUSB* 2>/dev/null || echo "未找到/dev/ttyUSB*设备"
ls /dev/ttyACM* 2>/dev/null || echo "未找到/dev/ttyACM*设备"

# 权限检查
if ls /dev/ttyUSB* >/dev/null 2>&1; then
    for device in /dev/ttyUSB*; do
        if [ -r "$device" ] && [ -w "$device" ]; then
            echo "✅ $device 权限正常"
        else
            echo "❌ 问题: $device 权限不足"
            echo "解决方案: sudo chmod 666 $device"
            echo "或添加用户到dialout组: sudo usermod -a -G dialout $USER"
            echo ""
        fi
    done
fi
echo ""

# 7. 网络和通信检查
echo "7. 网络和通信检查"
echo "=================="

# ROS_MASTER_URI检查
if [ -z "$ROS_MASTER_URI" ]; then
    echo "⚠️  ROS_MASTER_URI未设置，使用默认值"
    export ROS_MASTER_URI=http://localhost:11311
else
    echo "ROS_MASTER_URI: $ROS_MASTER_URI"
fi

# ROS_IP检查
if [ -z "$ROS_IP" ]; then
    echo "⚠️  ROS_IP未设置，可能影响多机通信"
else
    echo "ROS_IP: $ROS_IP"
fi
echo ""

# 8. 运行时检查
echo "8. 运行时检查"
echo "=============="

# 检查roscore是否运行
if pgrep -f "roscore" >/dev/null; then
    echo "✅ roscore正在运行"
    
    # 检查话题
    echo "当前话题列表:"
    timeout 5 rostopic list 2>/dev/null || echo "❌ 无法获取话题列表(roscore可能有问题)"
    
    # 检查节点
    echo "当前节点列表:"
    timeout 5 rosnode list 2>/dev/null || echo "❌ 无法获取节点列表"
    
else
    echo "❌ roscore未运行"
    echo "解决方案: 在新终端中执行 roscore"
fi
echo ""

# 9. 常见错误模式检查
echo "9. 常见错误模式检查"
echo "==================="

# 检查常见错误日志
if [ -d "$HOME/.ros/log" ]; then
    echo "检查最近的错误日志..."
    latest_log=$(find $HOME/.ros/log -name "*.log" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ -n "$latest_log" ]; then
        echo "最新日志文件: $latest_log"
        echo "错误信息摘要:"
        grep -i "error\|failed\|exception" "$latest_log" 2>/dev/null | tail -5 || echo "未发现明显错误"
    fi
fi
echo ""

# 10. 建议的解决步骤
echo "10. 建议的解决步骤"
echo "=================="
echo "基于以上检查，建议按以下顺序解决问题:"
echo ""
echo "1. 确保ROS环境正确设置"
echo "   source /opt/ros/$ROS_DISTRO/setup.bash"
echo ""
echo "2. 编译工作空间"
echo "   cd $WORKSPACE_PATH && catkin_make"
echo ""
echo "3. 设置工作空间环境"
echo "   source $WORKSPACE_PATH/devel/setup.bash"
echo ""
echo "4. 安装缺失的依赖包"
echo "   sudo apt-get install ros-$ROS_DISTRO-gmapping"
echo "   sudo apt-get install ros-$ROS_DISTRO-teleop-twist-keyboard"
echo ""
echo "5. 检查硬件连接和权限"
echo "   sudo chmod 666 /dev/ttyUSB*"
echo ""
echo "6. 逐步启动各个组件"
echo "   使用 step_by_step_gmapping.sh 脚本"
echo ""

echo "诊断完成时间: $(date)" >> $LOG_FILE
echo "详细日志已保存到: $LOG_FILE"
echo ""
echo "=== 诊断完成 ==="
