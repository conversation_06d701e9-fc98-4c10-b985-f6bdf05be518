# Gmapping调试工具包

这个工具包包含了三个脚本来帮助您调试和解决gmapping执行中遇到的问题。

## 文件说明

### 1. `debug_gmapping.sh` - 环境检查脚本
**功能**: 全面检查ROS环境、依赖包、launch文件等
**使用方法**:
```bash
chmod +x debug_gmapping.sh
./debug_gmapping.sh
```

### 2. `step_by_step_gmapping.sh` - 分步执行脚本  
**功能**: 逐步引导您执行gmapping的每个步骤，便于定位问题
**使用方法**:
```bash
chmod +x step_by_step_gmapping.sh
./step_by_step_gmapping.sh
```

### 3. `diagnose_gmapping_issues.sh` - 问题诊断脚本
**功能**: 深入诊断常见问题并提供解决方案
**使用方法**:
```bash
chmod +x diagnose_gmapping_issues.sh
./diagnose_gmapping_issues.sh
```

## 使用流程建议

### 第一步: 环境检查
```bash
./debug_gmapping.sh
```
这会检查您的ROS环境是否正确配置。

### 第二步: 问题诊断
```bash
./diagnose_gmapping_issues.sh
```
如果第一步发现问题，运行这个脚本获取详细的诊断和解决方案。

### 第三步: 分步执行
```bash
./step_by_step_gmapping.sh
```
按照引导逐步执行gmapping，每步都会验证是否成功。

## 常见问题及解决方案

### 问题1: "roscore command not found"
**原因**: ROS环境未设置
**解决**:
```bash
source /opt/ros/melodic/setup.bash  # 或noetic
echo 'source /opt/ros/melodic/setup.bash' >> ~/.bashrc
```

### 问题2: "No such file or directory: ~/310319/devel/setup.bash"
**原因**: 工作空间未编译
**解决**:
```bash
cd ~/310319
catkin_make
source devel/setup.bash
```

### 问题3: "Package 'gmapping' not found"
**原因**: gmapping包未安装
**解决**:
```bash
sudo apt-get update
sudo apt-get install ros-$ROS_DISTRO-gmapping
```

### 问题4: "Permission denied: /dev/ttyUSB0"
**原因**: 串口设备权限不足
**解决**:
```bash
sudo chmod 666 /dev/ttyUSB*
# 或永久解决
sudo usermod -a -G dialout $USER
# 然后重新登录
```

### 问题5: "No laser scan received"
**原因**: 激光雷达驱动问题
**解决**:
1. 检查硬件连接
2. 检查驱动程序
3. 验证话题: `rostopic echo /scan`

### 问题6: "Transform from base_link to laser does not exist"
**原因**: tf变换配置错误
**解决**:
1. 检查robot_with_imu.launch中的tf配置
2. 运行: `rosrun tf view_frames`
3. 检查tf树结构

### 问题7: "Gmapping not building map"
**原因**: 数据输入问题
**解决**:
1. 检查/scan话题有数据: `rostopic hz /scan`
2. 检查/odom话题有数据: `rostopic hz /odom`
3. 检查机器人是否在移动
4. 调整gmapping参数

## 参数调优建议

如果gmapping运行但建图效果不好，可以调整以下参数:

### 提高建图精度:
```xml
<param name="delta" value="0.01"/>          <!-- 地图分辨率1cm -->
<param name="particles" value="30"/>        <!-- 增加粒子数 -->
<param name="linearUpdate" value="0.02"/>   <!-- 更频繁更新 -->
```

### 提高运行速度:
```xml
<param name="delta" value="0.05"/>          <!-- 地图分辨率5cm -->
<param name="particles" value="5"/>         <!-- 减少粒子数 -->
<param name="map_update_interval" value="0.1"/> <!-- 降低更新频率 -->
```

### 适应大环境:
```xml
<param name="maxRange" value="15.0"/>       <!-- 增加测距范围 -->
<param name="xmin" value="-10.0"/>          <!-- 扩大地图范围 -->
<param name="xmax" value="10.0"/>
<param name="ymin" value="-10.0"/>
<param name="ymax" value="10.0"/>
```

## 保存地图

建图完成后保存地图:
```bash
rosrun map_server map_saver -f my_map
```

这会生成两个文件:
- `my_map.pgm` - 地图图像
- `my_map.yaml` - 地图元数据

## 故障排除检查清单

- [ ] ROS环境已设置 (`echo $ROS_DISTRO`)
- [ ] 工作空间已编译 (`ls ~/310319/devel/`)
- [ ] 工作空间环境已加载 (`rospack find robot_slam`)
- [ ] 依赖包已安装 (`rospack find gmapping`)
- [ ] 硬件设备已连接 (`ls /dev/ttyUSB*`)
- [ ] 设备权限正确 (`ls -l /dev/ttyUSB*`)
- [ ] roscore正在运行 (`rosnode list`)
- [ ] 激光雷达有数据 (`rostopic hz /scan`)
- [ ] 里程计有数据 (`rostopic hz /odom`)
- [ ] tf变换正常 (`rosrun tf tf_monitor`)

## 联系支持

如果使用这些工具后仍有问题，请提供:
1. 错误信息截图
2. `diagnose_gmapping_issues.sh`的输出
3. 相关日志文件内容
4. 硬件配置信息
