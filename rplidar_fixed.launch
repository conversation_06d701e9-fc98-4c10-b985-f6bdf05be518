<launch>
  <!-- RPLidar修复版本配置 -->
  
  <!-- 自动检测串口设备 -->
  <arg name="serial_port" default="/dev/ttyUSB0" />
  <arg name="serial_baudrate" default="115200" />
  <arg name="frame_id" default="laser_link" />
  <arg name="inverted" default="false" />
  <arg name="angle_compensate" default="true" />
  
  <node name="rplidarNode" pkg="rplidar_ros" type="rplidarNode" output="screen">
    <param name="serial_port" type="string" value="$(arg serial_port)"/>  
    <param name="serial_baudrate" type="int" value="$(arg serial_baudrate)"/>
    <param name="frame_id" type="string" value="$(arg frame_id)"/>
    <param name="inverted" type="bool" value="$(arg inverted)"/>
    <param name="angle_compensate" type="bool" value="$(arg angle_compensate)"/>
    
    <!-- 可选参数 -->
    <!--
    <param name="scan_mode" type="string" value="Sensitivity"/>
    <param name="min_distance" type="double" value="0.02"/>
    <param name="max_distance" type="double" value="8.0"/>
    -->
  </node>
  
  <!-- 激光雷达数据滤波 -->
  <include file="$(find lidar_filters)/launch/box_filter_example.launch"/>
  
</launch>
