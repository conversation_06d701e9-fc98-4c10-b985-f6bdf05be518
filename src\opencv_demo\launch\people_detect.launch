<launch>
  <arg name="node_name" default="people_detect" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="hit_threshold" default="0" doc="Threshold for the distance between features and SVM classifying plane" />
  <arg name="win_stride" default="8" doc="Window stride. It must be a multiple of block stride." />
  <arg name="padding" default="32" doc="Mock parameter to keep the CPU interface compatibility. It must be (0,0)." />
  <arg name="scale0" default="1.05" doc="Coefficient of the detection window increase." />
  <arg name="group_threshold" default="2" doc="Coefficient to regulate the similarity threshold. When detected, some objects can be covered by many rectangles. 0 means not to perform grouping." />

  <!-- people_detect.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="people_detect" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="hit_threshold" value="$(arg hit_threshold)" />
    <param name="win_stride" value="$(arg win_stride)" />
    <param name="padding" value="$(arg padding)" />
    <param name="scale0" value="$(arg scale0)" />
    <param name="group_threshold" value="$(arg group_threshold)" />
  </node>
</launch>
