<launch>
  <param name="use_sim_time" value="true" />
  <node name="cartographer_node" pkg="cartographer_ros"
        type="cartographer_node" args="
          -configuration_directory  $(find robot_slam)/params
          -configuration_basename zoo_2Dlidar_location_params.lua"
      output="screen">
   <remap from="scan" to="/scan" />
   <remap from="odom" to="/odom" />
   <remap from="imu" to="/imu" />
  </node> 

  <node name="rviz" pkg="rviz" type="rviz" required="true"
      args="-d $(find cartographer_ros)/configuration_files/demo_2d.rviz" />
</launch>


