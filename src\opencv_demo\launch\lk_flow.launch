<launch>
  <arg name="node_name" default="lk_flow" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show edge image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />
  <arg name="quality_level" default="0.01" doc="Parameter characterizing the minimal acceptance quality of image corners." />
  <arg name="min_distance" default="10"  doc="Minimum possible Euclidean distance between the returned corners."/>
  <arg name="block_size" default="3" doc="Size of an average block for computing a derivative covariation matrix over each pixel neighborhood."/>
  <arg name="harris_k" default="0.04" doc="Free parameter of the Harris detector"/>
  <!-- lk_flow.cpp -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="lk_flow" >
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="quality_level" value="$(arg quality_level)" />
    <param name="min_distance" value="$(arg min_distance)" />
    <param name="blcok_size" value="$(arg block_size)" />
    <param name="harris_k" value="$(arg harris_k)" />
  </node>
</launch>
