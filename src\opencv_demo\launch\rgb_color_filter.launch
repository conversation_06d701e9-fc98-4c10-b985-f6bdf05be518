<launch>
  <arg name="node_name" default="rgb_color_filter" />

  <arg name="image" default="image" doc="The image topic. Should be remapped to the name of the real image topic." />

  <arg name="use_camera_info" default="false" doc="Indicates that the camera_info topic should be subscribed to to get the default input_frame_id. Otherwise the frame from the image message will be used." />
  <arg name="debug_view" default="true" doc="Specify whether the node displays a window to show image" />
  <arg name="queue_size" default="3" doc="Specigy queue_size of input image subscribers" />

  <arg name="r_limit_max" default="250" doc="The maximum allowed field value Red" />
  <arg name="r_limit_min" default="50" doc="The minimum allowed field value Red" />
  <arg name="g_limit_max" default="200" doc="The maximum allowed field value Green" />
  <arg name="g_limit_min" default="100" doc="The minimum allowed field value Green" />
  <arg name="b_limit_max" default="150" doc="The maximum allowed field value Blue" />
  <arg name="b_limit_min" default="20" doc="The minimum allowed field value Blue" />

  <!-- color_filter.cpp  -->
  <node name="$(arg node_name)" pkg="opencv_apps" type="rgb_color_filter" output="screen">
    <remap from="image" to="$(arg image)" />
    <param name="use_camera_info" value="$(arg use_camera_info)" />
    <param name="debug_view" value="$(arg debug_view)" />
    <param name="queue_size" value="$(arg queue_size)" />
    <param name="r_limit_max" value="$(arg r_limit_max)" />
    <param name="r_limit_min" value="$(arg r_limit_min)" />
    <param name="g_limit_max" value="$(arg g_limit_max)" />
    <param name="g_limit_min" value="$(arg g_limit_min)" />
    <param name="b_limit_max" value="$(arg b_limit_max)" />
    <param name="b_limit_min" value="$(arg b_limit_min)" />
  </node>
</launch>
