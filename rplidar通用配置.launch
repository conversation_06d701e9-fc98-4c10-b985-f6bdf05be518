<launch>
  <!-- 通用RPLidar配置 - 适用于大多数型号 -->
  
  <!-- 可以通过命令行参数修改这些值 -->
  <arg name="serial_port" default="/dev/ttyUSB0" />
  <arg name="serial_baudrate" default="115200" />
  <arg name="frame_id" default="laser_link" />
  
  <node name="rplidarNode" pkg="rplidar_ros" type="rplidarNode" output="screen">
    <param name="serial_port" type="string" value="$(arg serial_port)"/>  
    <param name="serial_baudrate" type="int" value="$(arg serial_baudrate)"/>
    <param name="frame_id" type="string" value="$(arg frame_id)"/>
    <param name="inverted" type="bool" value="false"/>
    <param name="angle_compensate" type="bool" value="true"/>
    
    <!-- 添加超时和重试参数 -->
    <param name="scan_mode" type="string" value="Standard"/>
  </node>
  
  <!-- 注释掉滤波器，先测试基本功能 -->
  <!-- <include file="$(find lidar_filters)/launch/box_filter_example.launch"/> -->
  
</launch>
