#!/bin/bash

# ROS Gmapping调试脚本
# 用于检查和模拟执行gmapping启动过程

echo "=== ROS Gmapping 调试脚本 ==="
echo "开始检查ROS环境和依赖..."

# 1. 检查ROS环境
echo "1. 检查ROS环境..."
if [ -z "$ROS_DISTRO" ]; then
    echo "❌ 错误: ROS_DISTRO环境变量未设置"
    echo "   解决方案: source /opt/ros/melodic/setup.bash (或其他版本)"
else
    echo "✅ ROS版本: $ROS_DISTRO"
fi

# 2. 检查工作空间
echo "2. 检查工作空间..."
WORKSPACE_PATH="$HOME/310319"
if [ ! -d "$WORKSPACE_PATH" ]; then
    echo "❌ 错误: 工作空间路径不存在: $WORKSPACE_PATH"
    echo "   当前脚本假设路径: ~/310319/"
else
    echo "✅ 工作空间路径存在: $WORKSPACE_PATH"
fi

# 3. 检查devel/setup.bash
echo "3. 检查工作空间环境文件..."
SETUP_FILE="$WORKSPACE_PATH/devel/setup.bash"
if [ ! -f "$SETUP_FILE" ]; then
    echo "❌ 错误: setup.bash文件不存在: $SETUP_FILE"
    echo "   解决方案: cd ~/310319 && catkin_make"
else
    echo "✅ setup.bash文件存在"
fi

# 4. 检查必要的包
echo "4. 检查ROS包依赖..."
source $SETUP_FILE 2>/dev/null

# 检查gmapping包
if rospack find gmapping >/dev/null 2>&1; then
    echo "✅ gmapping包已安装"
else
    echo "❌ 错误: gmapping包未安装"
    echo "   解决方案: sudo apt-get install ros-$ROS_DISTRO-gmapping"
fi

# 检查abot_bringup包
if rospack find abot_bringup >/dev/null 2>&1; then
    echo "✅ abot_bringup包存在"
else
    echo "❌ 错误: abot_bringup包不存在"
    echo "   检查: $WORKSPACE_PATH/src/abot_base/abot_bringup/"
fi

# 检查robot_slam包
if rospack find robot_slam >/dev/null 2>&1; then
    echo "✅ robot_slam包存在"
else
    echo "❌ 错误: robot_slam包不存在"
    echo "   检查: $WORKSPACE_PATH/src/robot_slam/"
fi

# 检查teleop_twist_keyboard
if rospack find teleop_twist_keyboard >/dev/null 2>&1; then
    echo "✅ teleop_twist_keyboard包已安装"
else
    echo "❌ 错误: teleop_twist_keyboard包未安装"
    echo "   解决方案: sudo apt-get install ros-$ROS_DISTRO-teleop-twist-keyboard"
fi

# 5. 检查launch文件
echo "5. 检查launch文件..."
ROBOT_LAUNCH="$WORKSPACE_PATH/src/abot_base/abot_bringup/launch/robot_with_imu.launch"
if [ ! -f "$ROBOT_LAUNCH" ]; then
    echo "❌ 错误: robot_with_imu.launch文件不存在"
    echo "   路径: $ROBOT_LAUNCH"
else
    echo "✅ robot_with_imu.launch文件存在"
fi

GMAPPING_LAUNCH="$WORKSPACE_PATH/src/robot_slam/launch/gmapping.launch"
if [ ! -f "$GMAPPING_LAUNCH" ]; then
    echo "❌ 错误: gmapping.launch文件不存在"
    echo "   路径: $GMAPPING_LAUNCH"
else
    echo "✅ gmapping.launch文件存在"
fi

VIEW_LAUNCH="$WORKSPACE_PATH/src/robot_slam/launch/view_mapping.launch"
if [ ! -f "$VIEW_LAUNCH" ]; then
    echo "❌ 错误: view_mapping.launch文件不存在"
    echo "   路径: $VIEW_LAUNCH"
else
    echo "✅ view_mapping.launch文件存在"
fi

# 6. 检查硬件连接(仿真)
echo "6. 检查硬件连接..."
echo "⚠️  注意: 以下是常见硬件问题检查项:"
echo "   - 激光雷达是否连接并发布/scan话题"
echo "   - IMU是否正常工作"
echo "   - 底盘驱动是否正常"
echo "   - USB权限是否正确: sudo chmod 666 /dev/ttyUSB*"

# 7. 模拟执行步骤
echo "7. 模拟执行步骤..."
echo "步骤1: 启动roscore"
echo "   命令: roscore"
echo "   检查: rostopic list 应该显示基础话题"

echo "步骤2: 启动机器人硬件"
echo "   命令: roslaunch abot_bringup robot_with_imu.launch"
echo "   检查: rostopic list 应该显示 /scan, /imu, /odom 等话题"

echo "步骤3: 启动gmapping"
echo "   命令: roslaunch robot_slam gmapping.launch"
echo "   检查: rostopic list 应该显示 /map 话题"

echo "步骤4: 启动可视化"
echo "   命令: roslaunch robot_slam view_mapping.launch"
echo "   检查: RViz界面应该显示激光雷达数据和地图"

echo "步骤5: 启动遥控"
echo "   命令: rosrun teleop_twist_keyboard teleop_twist_keyboard.py"
echo "   检查: 按键应该能控制机器人移动"

# 8. 常见问题解决方案
echo "8. 常见问题解决方案..."
echo "问题1: 'roscore' command not found"
echo "   解决: source /opt/ros/melodic/setup.bash"

echo "问题2: 找不到包"
echo "   解决: source ~/310319/devel/setup.bash"

echo "问题3: 激光雷达无数据"
echo "   解决: 检查设备连接和驱动"

echo "问题4: tf变换错误"
echo "   解决: 检查robot_with_imu.launch中的tf配置"

echo "问题5: gmapping无法建图"
echo "   解决: 检查/scan话题数据和里程计数据"

echo "=== 调试完成 ==="
echo "建议: 逐步执行每个命令，检查每步的输出"
