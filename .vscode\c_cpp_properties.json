{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/310319/devel/include/**", "/opt/ros/melodic/include/**", "/home/<USER>/310319/src/abot_base/abot_bringup/include/**", "/home/<USER>/310319/src/abot_base/abot_imu/include/**", "/home/<USER>/310319/src/cam_track/include/**", "/home/<USER>/310319/src/color_pkg/include/**", "/home/<USER>/310319/src/face_pkg/include/**", "/home/<USER>/310319/src/abot_find/include/**", "/home/<USER>/310319/src/abot_base/lidar_filters/include/**", "/home/<USER>/310319/src/opencv_demo/include/**", "/home/<USER>/310319/src/robot_slam/include/**", "/home/<USER>/310319/src/robot_voice/include/**", "/home/<USER>/310319/src/shoot_cmd/include/**", "/home/<USER>/310319/src/track_tag/include/**", "/home/<USER>/310319/src/tracker_pkg/include/**", "/home/<USER>/310319/src/user_demo/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}