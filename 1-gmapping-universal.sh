#!/bin/bash

###gmapping with abot - 通用版本###
# 支持多种终端环境

# 设置工作空间路径
WORKSPACE_PATH="$HOME/310319"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查函数
check_workspace() {
    if [ ! -d "$WORKSPACE_PATH" ]; then
        print_error "工作空间不存在: $WORKSPACE_PATH"
        echo "请修改脚本中的WORKSPACE_PATH变量或创建工作空间"
        exit 1
    fi
    print_status "工作空间路径: $WORKSPACE_PATH"
}

check_setup_file() {
    if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
        print_error "setup.bash不存在，请先编译工作空间:"
        echo "cd $WORKSPACE_PATH && catkin_make"
        exit 1
    fi
    print_status "setup.bash文件存在"
}

check_packages() {
    source "$WORKSPACE_PATH/devel/setup.bash"
    
    # 检查必要的包
    packages=("abot_bringup" "robot_slam" "gmapping" "teleop_twist_keyboard")
    for pkg in "${packages[@]}"; do
        if rospack find $pkg >/dev/null 2>&1; then
            print_status "包 $pkg 已找到"
        else
            print_error "包 $pkg 未找到"
            if [ "$pkg" = "gmapping" ] || [ "$pkg" = "teleop_twist_keyboard" ]; then
                echo "安装命令: sudo apt-get install ros-\$ROS_DISTRO-$pkg"
            fi
        fi
    done
}

# 检测可用的终端
detect_terminal() {
    if command -v gnome-terminal >/dev/null 2>&1; then
        echo "gnome-terminal"
    elif command -v xterm >/dev/null 2>&1; then
        echo "xterm"
    elif command -v konsole >/dev/null 2>&1; then
        echo "konsole"
    elif command -v terminator >/dev/null 2>&1; then
        echo "terminator"
    else
        echo "none"
    fi
}

# 启动函数 - gnome-terminal版本
start_with_gnome() {
    print_status "使用gnome-terminal启动..."
    gnome-terminal --window -e 'bash -c "echo \"=== 启动roscore ===\"; roscore; exec bash"' \
    --tab -e "bash -c \"echo '=== 等待roscore启动 ==='; sleep 5; echo '=== 启动机器人硬件 ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup robot_with_imu.launch; exec bash\"" \
    --tab -e "bash -c \"echo '=== 等待硬件启动 ==='; sleep 8; echo '=== 启动gmapping ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch; exec bash\"" \
    --tab -e "bash -c \"echo '=== 等待gmapping启动 ==='; sleep 10; echo '=== 启动RViz ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch; exec bash\"" \
    --tab -e "bash -c \"echo '=== 等待系统就绪 ==='; sleep 12; echo '=== 启动键盘控制 ==='; echo '使用 i,j,k,l,, 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py; exec bash\""
}

# 启动函数 - xterm版本
start_with_xterm() {
    print_status "使用xterm启动..."
    xterm -hold -e "echo '=== 启动roscore ==='; roscore" &
    sleep 2
    xterm -hold -e "echo '=== 等待roscore启动 ==='; sleep 5; echo '=== 启动机器人硬件 ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch abot_bringup robot_with_imu.launch" &
    sleep 2
    xterm -hold -e "echo '=== 等待硬件启动 ==='; sleep 8; echo '=== 启动gmapping ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam gmapping.launch" &
    sleep 2
    xterm -hold -e "echo '=== 等待gmapping启动 ==='; sleep 10; echo '=== 启动RViz ==='; source $WORKSPACE_PATH/devel/setup.bash; roslaunch robot_slam view_mapping.launch" &
    sleep 2
    xterm -hold -e "echo '=== 等待系统就绪 ==='; sleep 12; echo '=== 启动键盘控制 ==='; echo '使用 i,j,k,l,, 控制机器人移动'; rosrun teleop_twist_keyboard teleop_twist_keyboard.py" &
}

# 手动启动函数
start_manual() {
    print_warning "未检测到图形终端，请手动在不同终端中执行以下命令:"
    echo ""
    echo "终端1 (roscore):"
    echo "roscore"
    echo ""
    echo "终端2 (机器人硬件) - 等待5秒后执行:"
    echo "source $WORKSPACE_PATH/devel/setup.bash"
    echo "roslaunch abot_bringup robot_with_imu.launch"
    echo ""
    echo "终端3 (gmapping) - 等待8秒后执行:"
    echo "source $WORKSPACE_PATH/devel/setup.bash"
    echo "roslaunch robot_slam gmapping.launch"
    echo ""
    echo "终端4 (RViz) - 等待10秒后执行:"
    echo "source $WORKSPACE_PATH/devel/setup.bash"
    echo "roslaunch robot_slam view_mapping.launch"
    echo ""
    echo "终端5 (键盘控制) - 等待12秒后执行:"
    echo "rosrun teleop_twist_keyboard teleop_twist_keyboard.py"
}

# 主程序
main() {
    echo "=== ROS Gmapping 启动脚本 ==="
    
    # 检查环境
    check_workspace
    check_setup_file
    check_packages
    
    # 检测终端并启动
    terminal=$(detect_terminal)
    case $terminal in
        "gnome-terminal")
            start_with_gnome
            ;;
        "xterm")
            start_with_xterm
            ;;
        *)
            start_manual
            ;;
    esac
    
    print_status "启动完成！"
    echo ""
    echo "使用说明:"
    echo "1. 等待所有组件启动完成"
    echo "2. 在RViz中检查激光雷达数据显示"
    echo "3. 使用键盘控制机器人移动进行建图"
    echo "4. 建图完成后保存地图: rosrun map_server map_saver -f my_map"
}

# 运行主程序
main
