{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/abot_ws/devel/include/**", "/opt/ros/melodic/include/**", "/home/<USER>/abot_ws/src/abot_base/abot_bringup/include/**", "/home/<USER>/abot_ws/src/abot_base/abot_imu/include/**", "/home/<USER>/abot_ws/src/cam_track/include/**", "/home/<USER>/abot_ws/src/color_pkg/include/**", "/home/<USER>/abot_ws/src/face_pkg/include/**", "/home/<USER>/abot_ws/src/abot_find/include/**", "/home/<USER>/abot_ws/src/abot_base/lidar_filters/include/**", "/home/<USER>/abot_ws/src/opencv_demo/include/**", "/home/<USER>/abot_ws/src/robot_slam/include/**", "/home/<USER>/abot_ws/src/robot_voice/include/**", "/home/<USER>/abot_ws/src/shoot_cmd/include/**", "/home/<USER>/abot_ws/src/track_tag/include/**", "/home/<USER>/abot_ws/src/tracker_pkg/include/**", "/home/<USER>/abot_ws/src/user_demo/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}