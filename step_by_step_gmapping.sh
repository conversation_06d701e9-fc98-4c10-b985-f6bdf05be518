#!/bin/bash

# 分步执行gmapping的脚本
# 用于逐步调试和验证每个步骤

WORKSPACE_PATH="$HOME/310319"

echo "=== 分步执行Gmapping脚本 ==="
echo "工作空间路径: $WORKSPACE_PATH"
echo ""

# 函数: 等待用户确认
wait_for_user() {
    echo "按Enter继续下一步，或输入'q'退出..."
    read -r input
    if [ "$input" = "q" ]; then
        echo "退出脚本"
        exit 0
    fi
}

# 函数: 检查进程是否运行
check_process() {
    local process_name=$1
    if pgrep -f "$process_name" > /dev/null; then
        echo "✅ $process_name 正在运行"
        return 0
    else
        echo "❌ $process_name 未运行"
        return 1
    fi
}

# 函数: 检查话题是否存在
check_topic() {
    local topic_name=$1
    if rostopic list 2>/dev/null | grep -q "$topic_name"; then
        echo "✅ 话题 $topic_name 存在"
        return 0
    else
        echo "❌ 话题 $topic_name 不存在"
        return 1
    fi
}

echo "步骤0: 环境准备"
echo "设置ROS环境变量..."
source /opt/ros/melodic/setup.bash 2>/dev/null || source /opt/ros/noetic/setup.bash 2>/dev/null
if [ -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    source "$WORKSPACE_PATH/devel/setup.bash"
    echo "✅ 工作空间环境已加载"
else
    echo "❌ 工作空间环境文件不存在，请先编译: cd $WORKSPACE_PATH && catkin_make"
fi
wait_for_user

echo "步骤1: 启动roscore"
echo "在新终端中执行: roscore"
echo "或者在后台启动..."
if ! check_process "roscore"; then
    echo "启动roscore..."
    roscore &
    sleep 3
    check_process "roscore"
fi
echo "检查基础话题:"
rostopic list 2>/dev/null || echo "roscore可能未正常启动"
wait_for_user

echo "步骤2: 启动机器人硬件驱动"
echo "执行命令: roslaunch abot_bringup robot_with_imu.launch"
echo "这将启动:"
echo "  - 激光雷达驱动"
echo "  - IMU驱动" 
echo "  - 底盘控制器"
echo "  - tf变换发布"
echo ""
echo "请在新终端中执行上述命令，然后回到这里继续..."
wait_for_user

echo "检查硬件驱动话题:"
check_topic "/scan"
check_topic "/imu"
check_topic "/odom"
echo "检查tf变换:"
rosrun tf tf_echo base_link laser 2>/dev/null || echo "❌ base_link到laser的tf变换不存在"
wait_for_user

echo "步骤3: 启动gmapping SLAM"
echo "执行命令: roslaunch robot_slam gmapping.launch"
echo "这将启动gmapping节点进行实时建图"
echo ""
echo "请在新终端中执行上述命令，然后回到这里继续..."
wait_for_user

echo "检查gmapping相关话题:"
check_topic "/map"
check_topic "/map_metadata"
echo "检查gmapping节点:"
check_process "slam_gmapping"
wait_for_user

echo "步骤4: 启动RViz可视化"
echo "执行命令: roslaunch robot_slam view_mapping.launch"
echo "这将打开RViz显示:"
echo "  - 激光雷达数据"
echo "  - 机器人位置"
echo "  - 实时构建的地图"
echo ""
echo "请在新终端中执行上述命令，然后回到这里继续..."
wait_for_user

echo "检查RViz:"
check_process "rviz"
wait_for_user

echo "步骤5: 启动键盘遥控"
echo "执行命令: rosrun teleop_twist_keyboard teleop_twist_keyboard.py"
echo "使用键盘控制机器人移动:"
echo "  i - 前进"
echo "  , - 后退"  
echo "  j - 左转"
echo "  l - 右转"
echo "  k - 停止"
echo ""
echo "请在新终端中执行上述命令，然后回到这里继续..."
wait_for_user

echo "检查遥控话题:"
check_topic "/cmd_vel"
wait_for_user

echo "步骤6: 验证整个系统"
echo "现在所有组件都应该在运行，请验证:"
echo "1. RViz中能看到激光雷达扫描数据(红色点)"
echo "2. 机器人模型显示正确"
echo "3. 键盘控制能让机器人移动"
echo "4. 移动时地图在实时更新"
echo "5. 地图中障碍物(黑色)和自由空间(白色)清晰"
echo ""

echo "=== 系统状态总结 ==="
echo "进程检查:"
check_process "roscore"
check_process "slam_gmapping"
check_process "rviz"

echo ""
echo "话题检查:"
check_topic "/scan"
check_topic "/odom"
check_topic "/map"
check_topic "/cmd_vel"

echo ""
echo "如果所有检查都通过，gmapping系统应该正常工作"
echo "如果有问题，请检查对应的错误信息和日志"
echo ""
echo "保存地图命令:"
echo "rosrun map_server map_saver -f my_map"
echo ""
echo "=== 脚本完成 ==="
