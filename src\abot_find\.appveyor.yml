
branches:
  only:
    - master
    - devel

os: Visual Studio 2015

clone_folder: c:\projects\find-object

platform: x64
configuration: Release

init:
  - cmake --version
  - call "C:\Program Files\Microsoft SDKs\Windows\v7.1\Bin\SetEnv.cmd" /x64
  - call "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" x86_amd64

install:
  # Qt
  - set QTDIR=C:\Qt\5.10.1\msvc2015_64
  # make sure Qt bin path is before cmake bin path to avoid copying qt5 dlls from cmake before qt installation
  - set PATH=%QTDIR%\bin;%PATH%
  # OpenCV
  - ps: wget 'https://dl.dropboxusercontent.com/s/o6ofn491bc0jso1/opencv450_vc14.exe?dl=0' -outfile opencv.exe
  - cmd: opencv.exe -o"C:\Program Files" -y
  - ECHO "Installed OpenCV:"
  - ps: "ls \"C:/Program Files/opencv\""
  - set PATH=%PATH%;C:\Program Files\opencv\x64\vc14\bin
  # zlib
  - ps: wget 'https://docs.google.com/uc?authuser=0&id=0B46akLGdg-uaYm9MTTI4MUtUcmc&export=download' -outfile zlib-1.2.8-vc2010-x64.zip
  - ps: Expand-Archive zlib-1.2.8-vc2010-x64.zip -DestinationPath 'C:\Program Files'
  - ECHO "Installed zlib:"
  - ps: "ls \"C:/Program Files/zlib\""
  - set PATH=%PATH%;C:\Program Files\zlib\bin
  
before_build:
  - cd c:\projects\find-object\build
  - ECHO %PROGRAMFILES%
  - ECHO %PATH%
  - cmake -G "Visual Studio 14 2015 Win64" -DOpenCV_DIR="C:\Program Files\opencv\build" ..
  
after_build :
  - cmake --build . --config Release --target package

artifacts:
  - path: build\Find-Object-*
  
notifications:
  - provider: Email
    to:
      - <EMAIL>
    on_build_success: false
    on_build_failure: false
    on_build_status_changed: true
