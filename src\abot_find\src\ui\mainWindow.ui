<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>mainWindow</class>
 <widget class="QMainWindow" name="mainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>881</width>
    <height>552</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Find-Object</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../resources.qrc">
    <normaloff>:/images/resources/Find-Object.png</normaloff>:/images/resources/Find-Object.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_8">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="margin">
     <number>0</number>
    </property>
    <item>
     <widget class="find_object::ImageDropWidget" name="imageDrop_scene" native="true">
      <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="margin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_5">
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Camera</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_timeRefreshRate">
           <property name="text">
            <string> (0 Hz - 0 Hz)</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_detectorDescriptorType">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_nfeatures">
           <property name="text">
            <string>0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_15">
           <property name="text">
            <string> features</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="find_object::ObjWidget" name="imageView_source" native="true"/>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QWidget" name="widget_controls" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="margin">
             <number>0</number>
            </property>
            <item>
             <widget class="QPushButton" name="pushButton_play">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>24</width>
                <height>24</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../resources.qrc">
                <normaloff>:/images/resources/TheWorkingGroup_video_play.ico</normaloff>:/images/resources/TheWorkingGroup_video_play.ico</iconset>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_pause">
              <property name="maximumSize">
               <size>
                <width>24</width>
                <height>24</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../resources.qrc">
                <normaloff>:/images/resources/TheWorkingGroup_video_pause.ico</normaloff>:/images/resources/TheWorkingGroup_video_pause.ico</iconset>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_stop">
              <property name="maximumSize">
               <size>
                <width>24</width>
                <height>24</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../resources.qrc">
                <normaloff>:/images/resources/TheWorkingGroup_video_stop.ico</normaloff>:/images/resources/TheWorkingGroup_video_stop.ico</iconset>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSlider" name="horizontalSlider_frames">
              <property name="focusPolicy">
               <enum>Qt::ClickFocus</enum>
              </property>
              <property name="minimum">
               <number>0</number>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_frame">
              <property name="text">
               <string>0</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>881</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionLoad_session"/>
    <addaction name="actionSave_session"/>
    <addaction name="separator"/>
    <addaction name="actionLoad_objects"/>
    <addaction name="actionSave_objects"/>
    <addaction name="separator"/>
    <addaction name="actionLoad_vocabulary"/>
    <addaction name="actionSave_vocabulary"/>
    <addaction name="separator"/>
    <addaction name="actionLoad_settings"/>
    <addaction name="actionSave_settings"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuEdit">
    <property name="title">
     <string>Edit</string>
    </property>
    <addaction name="actionAdd_object_from_scene"/>
    <addaction name="actionAdd_objects_from_files"/>
    <addaction name="separator"/>
    <addaction name="actionLoad_scene_from_file"/>
    <addaction name="actionCamera_from_directory_of_images"/>
    <addaction name="actionCamera_from_video_file"/>
    <addaction name="actionCamera_from_TCP_IP"/>
    <addaction name="separator"/>
    <addaction name="actionStart_camera"/>
    <addaction name="actionPause_camera"/>
    <addaction name="actionStop_camera"/>
    <addaction name="separator"/>
    <addaction name="actionRestore_all_default_settings"/>
    <addaction name="separator"/>
    <addaction name="actionRemove_all_objects"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>View</string>
    </property>
    <addaction name="actionHide_objects_features"/>
    <addaction name="actionShow_objects_features"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>?</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuEdit"/>
   <addaction name="menuView"/>
   <addaction name="menu"/>
  </widget>
  <widget class="QDockWidget" name="dockWidget_parameters">
   <property name="minimumSize">
    <size>
     <width>360</width>
     <height>168</height>
    </size>
   </property>
   <property name="floating">
    <bool>false</bool>
   </property>
   <property name="windowTitle">
    <string>Parameters</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents">
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="margin">
      <number>0</number>
     </property>
     <item>
      <widget class="find_object::ParametersToolBox" name="toolBox">
       <property name="currentIndex">
        <number>0</number>
       </property>
       <widget class="QWidget" name="page_1">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>348</width>
          <height>76</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Dummy</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_5"/>
       </widget>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_restoreDefaults">
       <property name="text">
        <string>Restore defaults</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidget_objects">
   <property name="minimumSize">
    <size>
     <width>208</width>
     <height>196</height>
    </size>
   </property>
   <property name="windowTitle">
    <string>Objects</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>1</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents_2">
    <layout class="QVBoxLayout" name="verticalLayout_6">
     <property name="margin">
      <number>0</number>
     </property>
     <item>
      <widget class="find_object::ImageDropWidget" name="imageDrop_objects" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="margin">
         <number>0</number>
        </property>
        <item>
         <widget class="QScrollArea" name="objects_area">
          <property name="minimumSize">
           <size>
            <width>150</width>
            <height>0</height>
           </size>
          </property>
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>222</width>
             <height>425</height>
            </rect>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_objects">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="margin">
             <number>0</number>
            </property>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>230</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>12</number>
          </property>
          <item>
           <widget class="QPushButton" name="pushButton_updateObjects">
            <property name="text">
             <string>Update objects</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSlider" name="horizontalSlider_objectsSize">
            <property name="minimumSize">
             <size>
              <width>20</width>
              <height>0</height>
             </size>
            </property>
            <property name="focusPolicy">
             <enum>Qt::ClickFocus</enum>
            </property>
            <property name="maximum">
             <number>100</number>
            </property>
            <property name="value">
             <number>100</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidget_plot">
   <property name="windowTitle">
    <string>Likelihood</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>8</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents_3">
    <layout class="QVBoxLayout" name="verticalLayout_7">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="margin">
      <number>0</number>
     </property>
     <item>
      <widget class="UPlot" name="likelihoodPlot" native="true"/>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidget_statistics">
   <property name="windowTitle">
    <string>Statistics</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents_4">
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QGridLayout" name="gridLayout" columnstretch="1,0,0">
       <property name="horizontalSpacing">
        <number>6</number>
       </property>
       <property name="verticalSpacing">
        <number>0</number>
       </property>
       <item row="5" column="1">
        <widget class="QLabel" name="label_timeIndexing">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QLabel" name="label_timeMatching">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="QLabel" name="label_9">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="QLabel" name="label_10">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="label_16">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="12" column="0">
        <widget class="QLabel" name="label_17">
         <property name="text">
          <string>Vocabulary size</string>
         </property>
        </widget>
       </item>
       <item row="12" column="1">
        <widget class="QLabel" name="label_vocabularySize">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="13" column="0">
        <widget class="QLabel" name="label_18">
         <property name="text">
          <string>IP address</string>
         </property>
        </widget>
       </item>
       <item row="14" column="0">
        <widget class="QLabel" name="label_19">
         <property name="text">
          <string>Output detection port</string>
         </property>
        </widget>
       </item>
       <item row="13" column="1">
        <widget class="QLabel" name="label_ipAddress">
         <property name="text">
          <string>0.0.0.0</string>
         </property>
        </widget>
       </item>
       <item row="14" column="1">
        <widget class="QLabel" name="label_port">
         <property name="text">
          <string>0</string>
         </property>
        </widget>
       </item>
       <item row="9" column="0">
        <widget class="QLabel" name="label_20">
         <property name="text">
          <string>Objects detected</string>
         </property>
        </widget>
       </item>
       <item row="9" column="1">
        <widget class="QLabel" name="label_objectsDetected">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QLabel" name="label_timeHomographies">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="15" column="0">
        <widget class="QLabel" name="label_21">
         <property name="text">
          <string>Input image port</string>
         </property>
        </widget>
       </item>
       <item row="15" column="1">
        <widget class="QLabel" name="label_port_image">
         <property name="text">
          <string>-</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_22">
         <property name="text">
          <string>Affine transforms</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>Descriptors indexing</string>
         </property>
        </widget>
       </item>
       <item row="7" column="2">
        <widget class="QLabel" name="label_12">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="10" column="1">
        <widget class="QLabel" name="label_minMatchedDistance">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QLabel" name="label_13">
         <property name="text">
          <string>Min matched distance</string>
         </property>
        </widget>
       </item>
       <item row="11" column="0">
        <widget class="QLabel" name="label_14">
         <property name="text">
          <string>Max matched distance</string>
         </property>
        </widget>
       </item>
       <item row="11" column="1">
        <widget class="QLabel" name="label_maxMatchedDistance">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="label_11">
         <property name="text">
          <string>Homograhies</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>Descriptors extraction</string>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QLabel" name="label_timeExtraction">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Features detection</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="label_timeDetection">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>Descriptors matching</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>Total</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="label_timeTotal">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLabel" name="label_timeSkewAffine">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="label_23">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QLabel" name="label_24">
         <property name="text">
          <string>Refresh GUI</string>
         </property>
        </widget>
       </item>
       <item row="8" column="1">
        <widget class="QLabel" name="label_timeRefreshGUI">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="8" column="2">
        <widget class="QLabel" name="label_25">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_26">
         <property name="text">
          <string>Features sub pixel refining</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLabel" name="label_timeSubPix">
         <property name="text">
          <string>000</string>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="label_27">
         <property name="text">
          <string>ms</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </widget>
  </widget>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionAdd_object_from_scene">
   <property name="text">
    <string>Add object from scene...</string>
   </property>
  </action>
  <action name="actionStart_camera">
   <property name="text">
    <string>Start camera</string>
   </property>
  </action>
  <action name="actionStop_camera">
   <property name="text">
    <string>Stop camera</string>
   </property>
  </action>
  <action name="actionSave_objects">
   <property name="text">
    <string>Save objects...</string>
   </property>
  </action>
  <action name="actionLoad_objects">
   <property name="text">
    <string>Load objects...</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About...</string>
   </property>
  </action>
  <action name="actionRestore_all_default_settings">
   <property name="text">
    <string>Restore all default settings</string>
   </property>
  </action>
  <action name="actionAdd_objects_from_files">
   <property name="text">
    <string>Add objects from files...</string>
   </property>
  </action>
  <action name="actionLoad_scene_from_file">
   <property name="text">
    <string>Camera from single file...</string>
   </property>
  </action>
  <action name="actionPause_camera">
   <property name="text">
    <string>Pause camera</string>
   </property>
  </action>
  <action name="actionCamera_from_video_file">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Camera from video file...</string>
   </property>
  </action>
  <action name="actionRemove_all_objects">
   <property name="text">
    <string>Remove all objects</string>
   </property>
  </action>
  <action name="actionCamera_from_directory_of_images">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Camera from directory of images...</string>
   </property>
  </action>
  <action name="actionSave_settings">
   <property name="text">
    <string>Save settings...</string>
   </property>
  </action>
  <action name="actionLoad_settings">
   <property name="text">
    <string>Load settings...</string>
   </property>
  </action>
  <action name="actionCamera_from_TCP_IP">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Camera from TCP/IP...</string>
   </property>
  </action>
  <action name="actionLoad_session">
   <property name="text">
    <string>Load session...</string>
   </property>
  </action>
  <action name="actionSave_session">
   <property name="text">
    <string>Save session...</string>
   </property>
  </action>
  <action name="actionHide_objects_features">
   <property name="text">
    <string>Hide objects features</string>
   </property>
  </action>
  <action name="actionShow_objects_features">
   <property name="text">
    <string>Show objects features</string>
   </property>
  </action>
  <action name="actionLoad_vocabulary">
   <property name="text">
    <string>Load vocabulary...</string>
   </property>
  </action>
  <action name="actionSave_vocabulary">
   <property name="text">
    <string>Save vocabulary...</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>find_object::ObjWidget</class>
   <extends>QWidget</extends>
   <header>find_object/ObjWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>find_object::ParametersToolBox</class>
   <extends>QToolBox</extends>
   <header>ParametersToolBox.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>UPlot</class>
   <extends>QWidget</extends>
   <header>utilite/UPlot.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>find_object::ImageDropWidget</class>
   <extends>QWidget</extends>
   <header>ImageDropWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../resources.qrc"/>
 </resources>
 <connections/>
</ui>
