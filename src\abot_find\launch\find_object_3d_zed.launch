<launch>
	<!-- Example finding 3D poses of the objects detected -->
	<!-- $ roslaunch zed_wrapper zed.launch -->

    <arg name="zed_prefix"    default="/zed/zed_node"/>
	<arg name="object_prefix" default="object"/>
	<arg name="objects_path"  default=""/>
	<arg name="gui"           default="true"/>
	<arg name="settings_path" default="~/.ros/find_object_2d.ini"/>
		
	<node name="find_object_3d" pkg="find_object_2d" type="find_object_2d" output="screen">
		<param name="gui" value="$(arg gui)" type="bool"/>
		<param name="settings_path" value="$(arg settings_path)" type="str"/>
		<param name="subscribe_depth" value="true" type="bool"/>
		<param name="objects_path" value="$(arg objects_path)" type="str"/>
		<param name="object_prefix" value="$(arg object_prefix)" type="str"/>
		<param name="approx_sync" value="false" type="bool"/>
		
		<remap from="rgb/image_rect_color" to="$(arg zed_prefix)/rgb/image_rect_color"/>
		<remap from="depth_registered/image_raw" to="$(arg zed_prefix)/depth/depth_registered"/>
		<remap from="depth_registered/camera_info" to="$(arg zed_prefix)/rgb/camera_info"/>
	</node>
</launch>
