<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?>

<cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********" moduleId="org.eclipse.cdt.core.settings" name="Unix">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********" name="Unix" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.306079558" name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.base.1536867206" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.1135530120" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
							<builder arguments="-C ${ProjDirPath}/build VERBOSE=true" command="make" id="cdt.managedbuild.target.gnu.builder.base.673178816" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.881773237" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.**********" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.424205109" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.86959710" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.467751471" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.921357809" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.2123576755" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.961100725" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.652879249" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets">
				<buildTargets>
					<target name="CMake-MinGW-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-MinGW-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-Unix-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-Unix-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
				</buildTargets>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********.759219509">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********.759219509" moduleId="org.eclipse.cdt.core.settings" name="Windows">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********.759219509" name="Windows" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.759219509." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.base.1456803292" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.2081668986" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
							<builder arguments="-j1" buildPath="${workspace_loc:/find-object/build}" command="jom" id="cdt.managedbuild.target.gnu.builder.base.1061057950" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.1544971303" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.2118555748" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.94612230" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.2131826214" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.793991581" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.425208659" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.857190324" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.814112417" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.1251214126" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1528092174" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets">
				<buildTargets>
					<target name="CMake-MinGW-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-MinGW-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-Unix-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
					<target name="CMake-Unix-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
						<buildCommand>cmake</buildCommand>
						<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
						<buildTarget/>
						<stopOnError>true</stopOnError>
						<useDefaultCommand>false</useDefaultCommand>
						<runAllBuilders>true</runAllBuilders>
					</target>
				</buildTargets>
			</storageModule>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="find_object.null.1999159200" name="find_object"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets">
		<buildTargets>
			<target name="CMake-MinGW-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>cmake</buildCommand>
				<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
				<buildTarget/>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>false</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="CMake-MinGW-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>cmake</buildCommand>
				<buildArguments>-E chdir build/ cmake -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
				<buildTarget/>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>false</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="CMake-Unix-Debug" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>cmake</buildCommand>
				<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Debug ../</buildArguments>
				<buildTarget/>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>false</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="CMake-Unix-Release" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>cmake</buildCommand>
				<buildArguments>-E chdir build/ cmake -G "Unix Makefiles" -D CMAKE_BUILD_TYPE=Release ../</buildArguments>
				<buildTarget/>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>false</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
		</buildTargets>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Windows">
			<resource resourceType="PROJECT" workspacePath="/find_object"/>
		</configuration>
		<configuration configurationName="Unix">
			<resource resourceType="PROJECT" workspacePath="/find_object"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.306079558;cdt.managedbuild.tool.gnu.cpp.compiler.base.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.424205109">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.306079558;cdt.managedbuild.tool.gnu.c.compiler.base.86959710;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>
