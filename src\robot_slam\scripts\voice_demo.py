#!/usr/bin/env python
# -*- coding: utf-8 -*-
import rospy
import os
#指定的音乐路径
kaishi="~/abot_ws/src/robot_voice/kaishi.mp3"
yi="~/abot_ws/src/robot_voice/shibie1.mp3"
er="~/abot_ws/src/robot_voice/shibie2.mp3"
san="~/abot_ws/src/robot_voice/shibie3.mp3"
si="~/abot_ws/src/robot_voice/shibie4.mp3"
wu="~/abot_ws/src/robot_voice/shibie5.mp3"
liu="~/abot_ws/src/robot_voice/shibie6.mp3"
qi="~/abot_ws/src/robot_voice/shibie7.mp3"
ba="~/abot_ws/src/robot_voice/shibie8.mp3"
tyi="~/abot_ws/src/robot_voice/dao1.mp3"
ter="~/abot_ws/src/robot_voice/dao2.mp3"
tsan="~/abot_ws/src/robot_voice/dao3.mp3"
tsi="~/abot_ws/src/robot_voice/dao4.mp3"
twu="~/abot_ws/src/robot_voice/dao5.mp3"
tliu="~/abot_ws/src/robot_voice/dao6.mp3"
tqi="~/abot_ws/src/robot_voice/dao7.mp3"
tba="~/abot_ws/src/robot_voice/dao8.mp3"
zd="~/abot_ws/src/robot_voice/dao9.mp3"


#使用播放器播放
os.system('mplayer %s' % kaishi)
os.system('mplayer %s' % yi)
os.system('mplayer %s' % er)
os.system('mplayer %s' % san)
os.system('mplayer %s' % si)
os.system('mplayer %s' % wu)
os.system('mplayer %s' % liu)
os.system('mplayer %s' % qi)
os.system('mplayer %s' % ba)
os.system('mplayer %s' % tyi)
os.system('mplayer %s' % ter)
os.system('mplayer %s' % tsan)
os.system('mplayer %s' % tsi)
os.system('mplayer %s' % twu)
os.system('mplayer %s' % tliu)
os.system('mplayer %s' % tqi)
os.system('mplayer %s' % tba)
os.system('mplayer %s' % zd)
