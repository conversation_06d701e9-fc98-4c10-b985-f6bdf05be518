// Generated by gencpp from file find_object_2d/ObjectsStamped.msg
// DO NOT EDIT!


#ifndef FIND_OBJECT_2D_MESSAGE_OBJECTSSTAMPED_H
#define FIND_OBJECT_2D_MESSAGE_OBJECTSSTAMPED_H


#include <string>
#include <vector>
#include <map>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <std_msgs/Float32MultiArray.h>

namespace find_object_2d
{
template <class ContainerAllocator>
struct ObjectsStamped_
{
  typedef ObjectsStamped_<ContainerAllocator> Type;

  ObjectsStamped_()
    : header()
    , objects()  {
    }
  ObjectsStamped_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , objects(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::std_msgs::Float32MultiArray_<ContainerAllocator>  _objects_type;
  _objects_type objects;





  typedef boost::shared_ptr< ::find_object_2d::ObjectsStamped_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::find_object_2d::ObjectsStamped_<ContainerAllocator> const> ConstPtr;

}; // struct ObjectsStamped_

typedef ::find_object_2d::ObjectsStamped_<std::allocator<void> > ObjectsStamped;

typedef boost::shared_ptr< ::find_object_2d::ObjectsStamped > ObjectsStampedPtr;
typedef boost::shared_ptr< ::find_object_2d::ObjectsStamped const> ObjectsStampedConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::find_object_2d::ObjectsStamped_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::find_object_2d::ObjectsStamped_<ContainerAllocator1> & lhs, const ::find_object_2d::ObjectsStamped_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.objects == rhs.objects;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::find_object_2d::ObjectsStamped_<ContainerAllocator1> & lhs, const ::find_object_2d::ObjectsStamped_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace find_object_2d

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsFixedSize< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::find_object_2d::ObjectsStamped_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct IsMessage< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::find_object_2d::ObjectsStamped_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::find_object_2d::ObjectsStamped_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
{
  static const char* value()
  {
    return "5ec2736b62b92d101276c97e8db387b1";
  }

  static const char* value(const ::find_object_2d::ObjectsStamped_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x5ec2736b62b92d10ULL;
  static const uint64_t static_value2 = 0x1276c97e8db387b1ULL;
};

template<class ContainerAllocator>
struct DataType< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
{
  static const char* value()
  {
    return "find_object_2d/ObjectsStamped";
  }

  static const char* value(const ::find_object_2d::ObjectsStamped_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# objects format: \n"
"# [ObjectId1, objectWidth, objectHeight, h11, h12, h13, h21, h22, h23, h31, h32, h33, ObjectId2...] \n"
"# where h## is a 3x3 homography matrix (h31 = dx and h32 = dy, see QTransform)\n"
"Header header\n"
"std_msgs/Float32MultiArray objects \n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Float32MultiArray\n"
"# Please look at the MultiArrayLayout message definition for\n"
"# documentation on all multiarrays.\n"
"\n"
"MultiArrayLayout  layout        # specification of data layout\n"
"float32[]         data          # array of data\n"
"\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/MultiArrayLayout\n"
"# The multiarray declares a generic multi-dimensional array of a\n"
"# particular data type.  Dimensions are ordered from outer most\n"
"# to inner most.\n"
"\n"
"MultiArrayDimension[] dim # Array of dimension properties\n"
"uint32 data_offset        # padding elements at front of data\n"
"\n"
"# Accessors should ALWAYS be written in terms of dimension stride\n"
"# and specified outer-most dimension first.\n"
"# \n"
"# multiarray(i,j,k) = data[data_offset + dim_stride[1]*i + dim_stride[2]*j + k]\n"
"#\n"
"# A standard, 3-channel 640x480 image with interleaved color channels\n"
"# would be specified as:\n"
"#\n"
"# dim[0].label  = \"height\"\n"
"# dim[0].size   = 480\n"
"# dim[0].stride = 3*640*480 = 921600  (note dim[0] stride is just size of image)\n"
"# dim[1].label  = \"width\"\n"
"# dim[1].size   = 640\n"
"# dim[1].stride = 3*640 = 1920\n"
"# dim[2].label  = \"channel\"\n"
"# dim[2].size   = 3\n"
"# dim[2].stride = 3\n"
"#\n"
"# multiarray(i,j,k) refers to the ith row, jth column, and kth channel.\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/MultiArrayDimension\n"
"string label   # label of given dimension\n"
"uint32 size    # size of given dimension (in type units)\n"
"uint32 stride  # stride of given dimension\n"
;
  }

  static const char* value(const ::find_object_2d::ObjectsStamped_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.objects);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ObjectsStamped_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::find_object_2d::ObjectsStamped_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::find_object_2d::ObjectsStamped_<ContainerAllocator>& v)
  {
    s << indent << "header: ";
    s << std::endl;
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    s << indent << "objects: ";
    s << std::endl;
    Printer< ::std_msgs::Float32MultiArray_<ContainerAllocator> >::stream(s, indent + "  ", v.objects);
  }
};

} // namespace message_operations
} // namespace ros

#endif // FIND_OBJECT_2D_MESSAGE_OBJECTSSTAMPED_H
