# Autogenerated param section. Do not hand edit.
param {
group.0 {
name=Dynamically Reconfigurable Parameters
desc=See the [[dynamic_reconfigure]] package for details on dynamically reconfigurable parameters.
0.name= ~wheel_diameter
0.default= 115
0.type= int
0.desc=The diameter of the wheel Range: 10 to 500
1.name= ~wheel_track
1.default= 300
1.type= int
1.desc=The track of the wheel Range: 50 to 1000
2.name= ~encoder_resolution
2.default= 1560
2.type= int
2.desc=The resolution of the encoder Range: 100 to 32000
3.name= ~do_pid_interval
3.default= 10
3.type= int
3.desc=The interval of do pid Range: 1 to 80
4.name= ~kp
4.default= 20
4.type= int
4.desc=Kp value Range: 0 to 10000
5.name= ~ki
5.default= 20
5.type= int
5.desc=Ki value Range: 0 to 32000
6.name= ~kd
6.default= 20
6.type= int
6.desc=Kd value Range: 0 to 1000
7.name= ~ko
7.default= 20
7.type= int
7.desc=Ko value Range: 0 to 1000
8.name= ~cmd_last_time
8.default= 200
8.type= int
8.desc=cmd_last_time value Range: 0 to 1000
9.name= ~max_v_liner_x
9.default= 60
9.type= int
9.desc=liner x Range: 0 to 500
10.name= ~max_v_liner_y
10.default= 0
10.type= int
10.desc=liner y Range: 0 to 500
11.name= ~max_v_angular_z
11.default= 120
11.type= int
11.desc=angular z Range: 0 to 2000
12.name= ~imu_type
12.default= 0
12.type= int
12.desc=imu type(`E`(69) for enable Range: 0 to 255
}
}
# End of autogenerated section. You may edit below.
