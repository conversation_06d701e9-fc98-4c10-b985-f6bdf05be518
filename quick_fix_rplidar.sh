#!/bin/bash

# RPLidar快速修复脚本
# 一键解决常见的rplidarNode错误

echo "=== RPLidar快速修复 ==="

# 1. 检查并修复设备权限
echo "1. 修复设备权限..."
sudo chmod 666 /dev/ttyUSB* 2>/dev/null
sudo chmod 666 /dev/ttyACM* 2>/dev/null
echo "✅ 权限修复完成"

# 2. 创建rplidar符号链接
echo "2. 创建设备符号链接..."
if [ -e "/dev/ttyUSB0" ]; then
    sudo rm -f /dev/rplidar 2>/dev/null
    sudo ln -s /dev/ttyUSB0 /dev/rplidar
    echo "✅ 已创建 /dev/rplidar -> /dev/ttyUSB0"
elif [ -e "/dev/ttyUSB1" ]; then
    sudo rm -f /dev/rplidar 2>/dev/null
    sudo ln -s /dev/ttyUSB1 /dev/rplidar
    echo "✅ 已创建 /dev/rplidar -> /dev/ttyUSB1"
else
    echo "❌ 未找到ttyUSB设备"
fi

# 3. 重新加载udev规则
echo "3. 重新加载udev规则..."
sudo udevadm control --reload-rules
sudo udevadm trigger

# 4. 显示当前设备状态
echo "4. 当前设备状态:"
ls -l /dev/ttyUSB* /dev/rplidar 2>/dev/null || echo "设备不存在"

echo ""
echo "=== 修复完成 ==="
echo "请重新启动gmapping系统"
echo ""
echo "测试命令:"
echo "rosrun rplidar_ros rplidarNode"
