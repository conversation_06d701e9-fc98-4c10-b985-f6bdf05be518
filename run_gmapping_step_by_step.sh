#!/bin/bash

###分步执行gmapping - 简化版###

WORKSPACE_PATH="$HOME/310319"

echo "=== 分步执行Gmapping ==="
echo "工作空间: $WORKSPACE_PATH"
echo ""

# 检查环境
if [ ! -f "$WORKSPACE_PATH/devel/setup.bash" ]; then
    echo "❌ 错误: 工作空间未编译"
    echo "请先执行: cd $WORKSPACE_PATH && catkin_make"
    exit 1
fi

echo "请按照以下步骤在不同终端中执行命令:"
echo ""

echo "步骤1: 启动roscore"
echo "======================================"
echo "在终端1中执行:"
echo "roscore"
echo ""
read -p "roscore启动完成后按Enter继续..."

echo "步骤2: 启动机器人硬件"
echo "======================================"
echo "在终端2中执行:"
echo "source $WORKSPACE_PATH/devel/setup.bash"
echo "roslaunch abot_bringup robot_with_imu.launch"
echo ""
echo "检查是否有以下话题发布:"
echo "rostopic list | grep -E '(scan|imu|odom)'"
echo ""
read -p "硬件启动完成后按Enter继续..."

echo "步骤3: 启动gmapping"
echo "======================================"
echo "在终端3中执行:"
echo "source $WORKSPACE_PATH/devel/setup.bash"
echo "roslaunch robot_slam gmapping.launch"
echo ""
echo "检查gmapping是否正常:"
echo "rostopic list | grep map"
echo "rosnode list | grep slam"
echo ""
read -p "gmapping启动完成后按Enter继续..."

echo "步骤4: 启动RViz可视化"
echo "======================================"
echo "在终端4中执行:"
echo "source $WORKSPACE_PATH/devel/setup.bash"
echo "roslaunch robot_slam view_mapping.launch"
echo ""
echo "在RViz中应该能看到:"
echo "- 激光雷达扫描数据(红色点)"
echo "- 机器人模型"
echo "- 地图(黑白网格)"
echo ""
read -p "RViz启动完成后按Enter继续..."

echo "步骤5: 启动键盘控制"
echo "======================================"
echo "在终端5中执行:"
echo "rosrun teleop_twist_keyboard teleop_twist_keyboard.py"
echo ""
echo "键盘控制说明:"
echo "i - 前进    , - 后退"
echo "j - 左转    l - 右转"
echo "k - 停止"
echo "q/z - 增加/减少速度"
echo ""
read -p "键盘控制启动完成后按Enter继续..."

echo "步骤6: 开始建图"
echo "======================================"
echo "现在可以开始建图了:"
echo "1. 使用键盘控制机器人缓慢移动"
echo "2. 观察RViz中地图的实时更新"
echo "3. 确保覆盖所有需要建图的区域"
echo "4. 避免快速转动，保持平稳移动"
echo ""
read -p "建图完成后按Enter继续..."

echo "步骤7: 保存地图"
echo "======================================"
echo "在新终端中执行:"
echo "cd $WORKSPACE_PATH"
echo "rosrun map_server map_saver -f my_map"
echo ""
echo "这将生成两个文件:"
echo "- my_map.pgm (地图图像)"
echo "- my_map.yaml (地图配置)"
echo ""

echo "=== 建图流程完成 ==="
echo ""
echo "故障排除:"
echo "- 如果没有激光数据: 检查激光雷达连接和驱动"
echo "- 如果地图不更新: 检查机器人是否在移动"
echo "- 如果tf错误: 检查robot_with_imu.launch中的tf配置"
echo "- 如果建图质量差: 调整gmapping参数或移动速度"
